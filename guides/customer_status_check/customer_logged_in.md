# Customer Logged In Status Check

## Use Case

Hide price info and cart from unauthenticated visitors.

Check out the [Customer Approval Check](./customer_approval_check.md) guide for feature to restrict only approved customers should be able to view price related to certain products.

## Scope

Include:

- checking if a customer is logged in

Exclude:

- check customer identity
- retrieve user info
- management or mechanism of user approval


## Description

### Dev Pre-Requisites

Shopify provides a managed service for customer login using a customised OAuth process. When a visitor wants to sign up / log in, they will be redirected to Shopify's authentication service (different domain and URL).

Shopify's security measures dictate that the redirection mentioned will only work if the origin URL is pre-registered. For example, user willl get an error message in the login page if they opens a login redirect link from a random FB post which is not pre-registered.

More info about Shopify's guide, see this [doc](https://shopify.dev/docs/storefronts/headless/building-with-the-customer-account-api/hydrogen#what-youll-learn).

To register the URLs, go to this [URL](https://admin.shopify.com/store/shop-gba/hydrogen/**********/settings/customer_api).

```
Storefront > Sales Channel > Hydrogen > Storefront Settings > Customer Account API > Application Setup
```

Oxygen's preview build will result in different URLs at each deployment, options are:

- after every deployment, manually copy the new URL and add it to the registry as mentioned earlier
- run  ngrok locally (without deployment to Oxygen), add the custom URL to registry as needed
- add a dev domain (can only be linked to the prod env), deploy from any branches / local to prod using the custom dev domain, and test the page using th dedicated domain

Ngrok allows us to do console.log() locally for debug need. Using custom domain will be the cloest set up to prod environment.

### Code

Using `app/routes/products.$handle.jsx` as an example.

#### Step 1 Customer Status Loader

Create a customer state loader function to query the customer logged in status in `context`.

```jsx
/**
 * Load customer's customer login status.
 * @param {LoaderFunctionArgs}
 */
function loadCustomerLoggedInStatus({context}) {
  const isLoggedInCustomer = context.customerAccount.isLoggedIn()
  return {
    isLoggedInCustomer,
  };
}
```

#### Step 2 - Update `loader` function

All loaders are called in the `loader` function. Update it and call our function.

```jsx
const isLoggedInCustomer = loadCustomerLoggedInStatus(args)
```

Update the return statement as well

```jsx
// isLoggedInCustomer is added to the object
return defer({...deferredData, ...criticalData, ...isLoggedInCustomer});
```

#### Step 3 - Resolve the Promise in the Product Function

Add `isLoggedInCustomer` promise from the loader and resolve it in the returned component for resoluiton.

Below is a simple example of the condition checks.

```jsx
function Product() {
    const {product, variants, isLoggedInCustomer} = useLoaderData();
    // ...
    return (
        <div>
            <Suspense>
                <Await resolve={isLoggedInCustomer}>
                {
                    (isRegisteredCustomer) => {
                    if (isRegisteredCustomer) {
                        return <p>Welcome Back</p>
                    } else {
                        return <p>Please Register</p>
                    }
                    }
                }
                </Await>
            </Suspense>
        </div>
    )
}
```
#### Step 4 - Hiding Price Info and Add to Cart Button

We just need to modify the existing components for price info and add to cart button so that they are nested under the conditional statements from Step 3 - only logged in users can see those components.

Existing components for price

```jsx
<ProductPrice
    price={selectedVariant?.price}
    compareAtPrice={selectedVariant?.compareAtPrice}
/>
```

Existing component for add to cart button

```
<Suspense
    fallback={
    <ProductForm
        product={product}
        selectedVariant={selectedVariant}
        variants={[]}
    />
    }
>
    <Await
    errorElement="There was a problem loading product variants"
    resolve={variants}
    >
    {(data) => (
        <ProductForm
        product={product}
        selectedVariant={selectedVariant}
        variants={data?.product?.variants.nodes || []}
        />
    )}
    </Await>
</Suspense>
```

Notice that there is already an Await resolution for this feature. [Promise.all()](https://github.com/remix-run/remix/discussions/6149) can be a solution. Nested Suspense Await blocks could also be another solution.

