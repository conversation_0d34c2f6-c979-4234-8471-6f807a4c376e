# Customer Log In Redirect

## Prerequisite

This document is only applicable if using the `New Customer API` for customer management. It is not applicable if customers are managed using the legacy Storefront API.

See this [article](https://www.shopify.com/partners/blog/introducing-customer-account-api-for-headless-stores) for details on the two options.

## Problem

In a previous proof of concept (PoC), we added a dedicated component showing a login button on the product page, allowing users to log in so they can view the product price.

The previous implementation simply directed users to the account page. Since the account page is protected by user authentication, users could log in using passwordless OTP. However, once authenticated, users remained on the account page.

For a better user experience, we want to redirect them back to the product page they were previously viewing.

## How OAuth Works (Kind Of)

Shopify's new Customer API is based on the [OAuth](https://shopify.dev/docs/api/customer#authentication) protocol.

At a high level, we use JavaScript in the user's web browser to create an HTTP request URL pointing to Shopify's OAuth Authorisation Server (required information is added as query parameters in the URL itself).

The JavaScript in the user's browser then initiates a redirection (HTTP 302), getting the user's web browser to open the crafted URL. At this point, the user is redirected to Shopify's authorisation server to log in.

Using the additional information attached to the URL as query parameters, the authorisation server can identify that the request originated from our web store (`client_id`) and issue an authorisation token that can later be exchanged for an access token.

Through the `client_id`, Shopify's authorisation server can look up the redirect URL we configured in Shopify's Storefront Customer settings [dashboard](https://admin.shopify.com/store/shop-gba/hydrogen/**********/settings/customer_api). This is the URL listed under `Callback URI(s) (required)`.

Shopify's authorisation server responds to the user's browser with an HTTP 302, redirecting the user back to the `Callback URI` configured in our web store (e.g. `https://xxx.ngrok-free.app/account/authorize`).

The authorisation token is attached to that 302 redirect URI as a query parameter, e.g. `/account/authorize?code=xxxx&state=xxxxx`.

Using the same HTTP 302 redirect mechanism, the web browser's JavaScript runtime uses the authorisation token to obtain an access token.

The access token can then be used to query information about the customer using the Customer API.

Note that Shopify's authorisation server will always redirect users back to the preconfigured `Callback URI` for security reasons. To redirect users back to the product page they started from, we need to store the location of that product page somewhere so that, when users are redirected back to the callback URI, the browser's JavaScript can use that information to redirect users back to the original product page.

If we need to do this manually, we can use the `state` [query parameter](https://datatracker.ietf.org/doc/html/rfc6749#section-4.1.1) to pass along the user's previous web location (the product page they were on).

Fortunately, Shopify's Hydrogen library simplifies this process by abstracting it. The section above provides a quick overview of what happens behind the scenes (some details have been omitted to focus on relevant parts) in case we need to create a customised customer client in the future.

## Hydrogen Library

The `LoginToViewPriceButton` component in `app/components/LoginToViewPriceButton.jsx` is used in the product route to hide price information from unauthenticated users.

[CustomerAccount](https://shopify.dev/docs/api/hydrogen/2024-10/utilities/createhydrogencontext#returns-propertydetail-customeraccount) contains a `login()` function call that, by default, redirects users back to their original page.

However, it must be called in a `loader` or `action` function in Remix. The `app/routes/account_.login.jsx` file calls the `login()` function in its loader function.

Since we want a button-click user experience, the easiest way is to turn it into an HTML form submission to the `/account/login` route. Hydrogen recognises that the submission using HTTP POST originated from the product page and will redirect users back to the product page by default.

All that's left to do is create an `action` function in `app/routes/account_.login.jsx` to handle the HTTP POST request, following Remix's default pattern.

```jsx
export async function action({request, context}) {
  return context.customerAccount.login();
}
