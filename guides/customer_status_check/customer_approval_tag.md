# Customer Approval Check

## Use Case

Only approved customer users can view price info.

## Scope

Include:

- check if a customer profile contains an `Approved` tag at front end and hide price info accordingly

Exclude:

- user approval status check at the backend

## Description

When visitors open a product page, Remix backend makes a number of GraphQL requests to Shopify's Product APIs to retrieve info about the product, including price and product description, etc.

To hide the price from the user, there are 2 steps:

**Step 1:** We hide the info at the front end. This approach requires creating some new components as alternative to the existing components that renders price details.

When visitors open the page, JS at browser will send a request to Remix backend to check if the user's profile contains `approved` customer tag. Browser will load the normal product components with price info if the tag exists. Otherwise it will load another component that doesn't contain the price info.

**Step 2:** Security enhancement. We add user tag checks at the remix backend and create different GraphQL queries for requests from unapproved users. Alternatively, we can also try modify and remove the price info before they are sent back to the front end.

Technically, Step 1 hide the price info. Step 2 makes sure that the price info can only be quried by approved users.

As first iteration, this guide includes info for Step 1.

### Dev Pre-Requisites

The pre-requisite set up info from the [customer logged in](./customer_logged_in.md) guide also applies here.

Specifically, Ngrok and Dev URL registraitons are required to interact with Shopify's Customer API end point.

While not strictly required for this guide, it is a good idea to get a good understanding of Shopify's Customer API [documentation](https://shopify.dev/docs/api/customer).

The scaffolding tool from Hydrogen included some libraries to make interaction with the API's GraphQL easier. However, if you want to use separate GraphQL clients to interact with Shopify's Customer API directly, you will need to know how to generate an access token (not covered by this guide).


### Code

Using `app/routes/products.$handle.jsx` as an example.

#### Step 1 Include Tags in Customer Account Query

Existing project already has a GraqhQL query for customer account in `app/graphql/customer-account/CustomerDetailsQuery.js`. We need to update it to include customer tags info. This [doc](https://shopify.dev/docs/api/customer/2024-01/queries/customer) has more info about available fields.

```jsx
export const CUSTOMER_FRAGMENT = `#graphql
  fragment Customer on Customer {
    id
    firstName
    lastName
    tags
    ...
}
```

This query is already used in `app/routes/account.jsx` in the generated project. We can quickly test it by modifying the `AccountLayout` component (see the example checks on the tags).

```jsx
export default function AccountLayout() {
  /** @type {LoaderReturnData} */
  const {customer} = useLoaderData();
  console.log(customer.tags)
  const heading = customer
    ? customer.firstName
      ? `Welcome, ${customer.firstName}`
      : `Welcome to your account.`
    : 'Account Details';

  const customerApprovalStatus = customer
    ? customer.tags
      ? customer.tags.some(tag => tag.toLowerCase() === 'approved')
        ? '(Customer Account Approved)'
        : `(Customer Account Pending Approval)`
      : 'Customer Account Unavailable'
    : 'Customer Account Unavailable';

  return (
    <div className="account">
      <h1>{heading}</h1>
      <p>{customerApprovalStatus}</p>
      <br />
      <AccountMenu />
      <br />
      <br />
      <Outlet context={{customer}} />
    </div>
  );
}
```

You can check the web page to verify the message.

#### Step 2 - Update `app/routes/products.$handle.jsx` as Example

If previous step is working as expected, we can then update the product page.

By default, the product page does not query customer data. Import the query first (top of file).

```jsx
import {CUSTOMER_DETAILS_QUERY} from '~/graphql/customer-account/CustomerDetailsQuery';
```

Create a new function to load customer details

```jsx
/**
 * Load customer details
 * @param {LoaderFunctionArgs}
 */
async function loadCustomerDetails({context}) {
  const {data, errors} = await context.customerAccount.query(
    CUSTOMER_DETAILS_QUERY,
  );

  return {customer: data.customer};
}
```

Then call that function from the `loader` function.

```jsx
/**
 * @param {LoaderFunctionArgs} args
 */
export async function loader(args) {

  const deferredData = loadDeferredData(args);

  const criticalData = await loadCriticalData(args);

  const isLoggedInCustomer = loadCustomerLoggedInStatus(args);

  const customer = await loadCustomerDetails(args);

  return defer({...deferredData, ...criticalData, ...isLoggedInCustomer, ...customer});
}
```

At this point, the `customer` object will become available to the front end (in the `Product` component).

Updte `Product` component to use the `customer` object.

```jsx
export default function Product() {
  /** @type {LoaderReturnData} */
  const {product, variants, isLoggedInCustomer, customer} = useLoaderData();
  //   ...
}
```

The customer tags are available as `customer.tags` in the `Product` component as an array.

#### Step 3 - Hide Price info at Front End

Using the tags info the from previous step. As proof of concept, we can wrap the existing `ProductPrice` component in a conditional statement.

```jsx
export default function Product() {
  // ...   

  return (
    <div className="product">
        // ...
        <Suspense>
          <Await resolve={customer}>
            {
              (customer) => {
                if (customer.tags.some(tag => tag.toLowerCase() === 'approved')) {
                  return ( <>
                    <ProductPrice
                      price={selectedVariant?.price}
                      compareAtPrice={selectedVariant?.compareAtPrice}
                    />
                  </>)
                } else {
                  return <p>Price info available to approved users only.</p>
                }
              }
            }
          </Await>
        </Suspense>
        // ...
    </div>
  )
}
```
