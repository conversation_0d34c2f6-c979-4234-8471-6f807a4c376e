# GBA Hydrogen Builder

Project based on generated template. See [TEMPLATE_README](./docs/TEMPLATE_README.md) for more info.

Intro to Hydrogen and Oxygen [doc](https://shopify.dev/docs/storefronts/headless/hydrogen/fundamentals).

## Dev Notes

Activate [Hermit](https://cashapp.github.io/hermit/usage/get-started/) dev env. The `/bin` dir is managed by Hermit.

```bash
. ./bin/activate-hermit
```

To set up local env / authentication

```bash
npm run env
```

Build the web

```bash
npm run dev

npm run build
```

### Code Structure

- `/app`:  holds all the core application code
- `/serverjs`: configures the server environment, typically setting up the app to handle server-side rendering (SSR) and any required middleware.

    It initializes the Hydrogen framework, connects to Shopify APIs, and defines how requests are processed.

    This file often includes code to manage routing, caching, error handling, and static asset serving, making it essential for managing the app’s server-side behavior.

