@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .round {
    @apply rounded-2xl;
  }
  .flex-center {
    @apply flex justify-center items-center;
  }
  .blackdrop-50 {
    @apply bg-black bg-opacity-50;
  }
  .hover-shadow {
    @apply shadow-none hover:shadow-2xl;
  }
  .btn-grid {
    @apply bg-white hover:bg-gba-blue text-black hover:text-white rounded-2xl shadow-none hover:shadow-2xl;
  }
}

:root {
  --aside-width: 500px;
  --cart-aside-summary-height-with-discount: 300px;
  --cart-aside-summary-height: 300px;
  --grid-item-width: 250px;
  --header-height: 120px;
  --color-dark: #000;
  --color-light: #fff;
  --text-color: #425b76;

  --sides: 1rem;
  --modal-sides: 0.75rem;
  --header-height: 3rem;
  --top-spacing: 5rem;
  --height-fold: 100vh;
  --height-fold: 100svh;

  @screen md {
    --sides: 1.5rem;
    --modal-sides: 1rem;
    /* --top-spacing: 9rem; */
  }

  @screen lg {
    --modal-sides: 1.5rem;
  }
}

@media (max-width: 480px) {
  :root {
    --aside-width: 100vw;
  }
}

/* img {
  border-radius: 1rem;
} */

.page {
  background: #f4f3ee;
  color: var(--text-color);
}

.fade {
  transition: opacity 0.55s;
}

.page-container {
  max-width: 1200px;
}

/*
* --------------------------------------------------
* components/Aside
* --------------------------------------------------
*/
/* aside {
  background: var(--color-light);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
  height: 100vh;
  max-width: var(--aside-width);
  min-width: var(--aside-width);
  position: fixed;
  top: 0;
  right: 0;
} */

aside header {
  align-items: center;
  border-bottom: 1px solid var(--color-dark);
  display: flex;
  height: var(--header-height);
  justify-content: space-between;
  padding: 0 20px;
}

aside header h3 {
  margin: 0;
}

aside header .close {
  font-weight: bold;
  opacity: 0.8;
  text-decoration: none;
  transition: all 200ms;
  width: 20px;
}

aside header .close:hover {
  opacity: 1;
}

aside header h2 {
  margin-bottom: 0.6rem;
  margin-top: 0;
}

aside main {
  margin: 1rem;
}

aside p {
  margin: 0 0 0.25rem;
}

aside p:last-child {
  margin: 0;
}

aside li {
  margin-bottom: 0.125rem;
}

.overlay {
  background: rgba(0, 0, 0, 0.2);
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 100;
}

.overlay .close-outside {
  background: transparent;
  border: none;
  color: transparent;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: calc(100% - var(--aside-width));
}

.overlay .light {
  background: rgba(255, 255, 255, 0.5);
}

.overlay .cancel {
  cursor: default;
  height: 100%;
  position: absolute;
  width: 100%;
}

button.reset {
  border: 0;
  background: inherit;
  font-size: inherit;
}

button.reset > * {
  margin: 0;
}

button.reset:not(:has(> *)) {
  height: 1.5rem;
  line-height: 1.5rem;
}

button.reset:hover:not(:has(> *)) {
  text-decoration: underline;
  cursor: pointer;
}

/*
* --------------------------------------------------
* components/Header
* --------------------------------------------------
*/
.header {
  /* position: sticky; */
  position: fixed;
  top: 0;
  z-index: 20;
  /* background: #e3e7eb; */
  color: var(--text-color);
  /* border-color: #c4c6c9; */
  /* border-bottom-width: 1px; */
}

.header-main {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--header-height);
  /* padding: 0 1rem; */
}

.header-menu-mobile-toggle {
  @media (min-width: 48em) {
    display: none;
  }
}

.header-menu-mobile {
  display: flex;
  flex-direction: column;
  grid-gap: 1rem;
  padding: 1rem 0;
  overflow-y: scroll;
}

.header-menu-desktop {
  display: none;
  grid-gap: 1rem;
  @media (min-width: 45em) {
    display: flex;
    height: 100%;
    /* grid-gap: 2rem; */
    /* margin-left: 3rem; */
  }
}

.header-dropdown {
  position: absolute;
  width: 100%;
  height: auto;
  background: #e3e7eb;
  border-color: #e5e7eb;
  border-top-width: 1px;
  border-bottom-width: 1px;
  /* display: flex;
  justify-content: center; */
}

.header-menu-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 100%;
  /* padding: 0 1.5rem; */
  font-weight: normal;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.header-ctas {
  align-items: center;
  display: flex;
  grid-gap: 1rem;
  margin-left: auto;
}

/*
* --------------------------------------------------
* components/Breadcrumbs
* --------------------------------------------------
*/
.breadcrumbs a.active {
  font-weight: bold;
  pointer-events: none;
}

/*
* --------------------------------------------------
* components/Footer
* --------------------------------------------------
*/
.footer {
  /* background: linear-gradient(to bottom, #fff, #25aadd); */
  margin-top: auto;
}

.footer .section-catalogues {
  /* rgba(66, 91, 118, 0.25) */
  background: rgba(66, 91, 118, 0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer .section-socials {
  background: rgba(66, 91, 118, 0.5);
  display: flex;
  justify-content: center;
}

.footer .section-details {
  background: rgba(66, 91, 118, 0.75);
}

.footer-menu {
  background: #425b76;
  align-items: center;
  display: flex;
  grid-gap: 1rem;
  padding: 1rem;
}

.category-menu {
  height: 55px;
  width: 500px;
  display: flex;
  justify-content: space-evenly;
}

/*
* --------------------------------------------------
* components/Cart
* --------------------------------------------------
*/
.cart-main {
  height: 100%;
  max-height: calc(100vh - var(--cart-aside-summary-height));
  overflow-y: auto;
  width: auto;
}

.cart-main.with-discount {
  max-height: calc(100vh - var(--cart-aside-summary-height-with-discount));
}

.cart-line {
  display: flex;
  padding: 0.75rem 0;
}

.cart-line img {
  height: 100%;
  display: block;
  margin-right: 0.75rem;
}

.cart-summary-page {
  position: relative;
}

.cart-summary-aside {
  background: white;
  border-top: 1px solid var(--color-dark);
  bottom: 0;
  padding-top: 0.75rem;
  position: absolute;
  width: calc(var(--aside-width) - 40px);
}

.cart-line-quantity {
  display: flex;
}

.cart-discount {
  align-items: center;
  display: flex;
  margin-top: 0.25rem;
}

.cart-subtotal {
  align-items: center;
  display: flex;
}
/*
* --------------------------------------------------
* components/Search
* --------------------------------------------------
*/
.predictive-search {
  height: calc(100vh - var(--header-height) - 40px);
  overflow-y: auto;
}

.predictive-search-form {
  background: var(--color-light);
  position: sticky;
  top: 0;
}

.predictive-search-result {
  margin-bottom: 2rem;
}

.predictive-search-result h5 {
  text-transform: uppercase;
}

.predictive-search-result-item {
  margin-bottom: 0.5rem;
}

.predictive-search-result-item a {
  align-items: center;
  display: flex;
}

.predictive-search-result-item a img {
  margin-right: 0.75rem;
  height: 100%;
}

.search-result {
  margin-bottom: 1.5rem;
}

.search-results-item {
  margin-bottom: 0.5rem;
}

.search-results-item a {
  display: flex;
  flex: row;
  align-items: center;
  gap: 1rem;
}

/*
* --------------------------------------------------
* routes/__index
* --------------------------------------------------
*/
.featured-collection {
  display: block;
  margin-bottom: 2rem;
  position: relative;
}

.featured-collection-image {
  aspect-ratio: 1 / 1;
  @media (min-width: 45em) {
    aspect-ratio: 16 / 9;
  }
}

.featured-collection img {
  height: auto;
  max-height: 100%;
  object-fit: cover;
}

.recommended-products-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(2, 1fr);
  @media (min-width: 45em) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.recommended-product img {
  height: auto;
}

/*
* --------------------------------------------------
* routes/collections._index.tsx
* --------------------------------------------------
*/
.collections-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.collection-item img {
  height: auto;
}

/*
* --------------------------------------------------
* routes/collections.$handle.tsx
* --------------------------------------------------
*/
.collection {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.collection-header {
  position: sticky;
  top: var(--header-height);
  /* background: linear-gradient(
    to bottom,
    rgba(243, 243, 243, 1) 50%,
    transparent
  ); */
  background: rgba(243, 243, 243, 1);
  z-index: 10;

  padding: 1em;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.collection-description {
  margin-bottom: 1rem;
  max-width: 95%;
  @media (min-width: 45em) {
    max-width: 600px;
  }
}

.products-grid {
  display: grid;
  grid-gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  grid-template-columns: repeat(2, minmax(0, var(--grid-item-width)));
  @media (min-width: 45em) {
    grid-template-columns: repeat(4, minmax(0, var(--grid-item-width)));
  }
}

/*
* --------------------------------------------------
* routes/products.$handle.tsx
* --------------------------------------------------
*/
.product {
  display: grid;
  @media (min-width: 45em) {
    grid-template-columns: 1fr 1fr;
    grid-gap: 1rem;
  }
}

.product h1 {
  margin-top: 0;
}

.product-image img {
  height: auto;
  width: 100%;
}

.product-price-on-sale {
  display: flex;
  grid-gap: 0.5rem;
}

.product-price-on-sale s {
  opacity: 0.5;
}

.product-options-grid {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 0.75rem;
}

.product-options-item {
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

.product-description ul {
  list-style-type: disc;
  padding: 1em 2em;
}

.product-table tr {
  border-bottom: 1px solid #d1d5db;
}

.product-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.product-table tr:hover {
  background: #d1d5db;
}

.product-table td {
  padding: 0.5em 1em;
}

/*
* --------------------------------------------------
* routes/blog._index.tsx
* --------------------------------------------------
*/
.blog-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.blog-article-image {
  aspect-ratio: 3/2;
  display: block;
}

.blog-article-image img {
  height: 100%;
}

/*
* --------------------------------------------------
* routes/blog.$articlehandle.tsx
* --------------------------------------------------
*/
.article img {
  height: auto;
  width: 100%;
}

/*
* --------------------------------------------------
* routes/account
* --------------------------------------------------
*/

.account-logout {
  display: inline-block;
}

.account-profile .field {
  display: flex;
  flex-direction: column;
}

.account-profile .field label {
  font-weight: bold;
  padding: 0.5em 0.22em;
}

.account-profile .field input,
.account-profile .field textarea {
  padding: 0.5em 0.77em;
  border-radius: 4px;
  border: 1px solid #000;
}

.account-profile .field input:disabled {
  background-color: grey;
  color: white;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
