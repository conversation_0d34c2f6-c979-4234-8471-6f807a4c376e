import {clsx} from 'clsx';
import {twMerge} from 'tailwind-merge';

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function getRelativeUrl({item, publicStoreDomain, primaryDomainUrl}) {
  if (!item.url) {
    return '';
  }
  // if the url is internal, we strip the domain
  const url =
    item.url.includes('myshopify.com') ||
    item.url.includes(publicStoreDomain) ||
    item.url.includes(primaryDomainUrl)
      ? new URL(item.url).pathname
      : item.url;
  return url;
}

export function isCategoryPage(menuItem) {
  if (menuItem?.resource?.showSubcategories?.value === 'true') {
    return true;
  }

  return false;
}

export function getMenuUrl(menuItem) {
  //console.log(menuItem);
  if (menuItem?.resource?.alternateLink?.value) {
    return menuItem.resource.alternateLink.value;
  }

  if (menuItem?.resource?.handle && isCategoryPage(menuItem)) {
    return `/categories/${menuItem.resource.handle}`;
  }

  if (menuItem?.type === 'COLLECTION') {
    return `/collections/${menuItem.resource.handle}`;
  }

  if (menuItem?.url) {
    if (menuItem.url.includes('myshopify.com')) {
      return new URL(menuItem.url).pathname;
    }
    return menuItem.url;
  }

  return undefined;
}

export function findBreadcrumbsByHandle(categories, handle) {
  for (const category of categories) {
    if (category.url.includes(handle)) {
      return [category];
    }

    if (category.items) {
      const subBreadcrumbs = findBreadcrumbsByHandle(category.items, handle);
      if (subBreadcrumbs.length) {
        return [category, ...subBreadcrumbs];
      }
    }
  }
  return [];
}

export function findBreadcrumbsByTitles(categories, titles) {
  for (const category of categories) {
    if (category.title === 'Specials') continue;
    if (titles.includes(category.title)) {
      const breadcrumbs = [category];

      if (category.items) {
        const subBreadcrumbs = findBreadcrumbsByTitles(category.items, titles);
        return breadcrumbs.concat(subBreadcrumbs);
      }

      return breadcrumbs;
    }
  }
  return [];
}

export function getImageAspectRatio(image, aspectRatio) {
  if (aspectRatio === 'adapt') {
    if (image?.width && image?.height) {
      return `${image.width}/${image.height}`;
    }
    return '1/1';
  }
  return aspectRatio;
}
