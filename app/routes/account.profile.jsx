import {
  CUSTOMER_UPDATE_MUTATION,
  CUSTOMER_UPDATE_METAFIELDS_MUTATION,
} from '~/graphql/customer-account/CustomerUpdateMutation';
import {json} from '@shopify/remix-oxygen';
import {Suspense} from 'react';
import {Await, useOutletContext} from '@remix-run/react';
import {EditProfileForm} from '~/components/EditProfileForm';
import * as Message from '~/components/AccountMessage';
import {Loading} from '~/components/Loading';

/**
 * @type {MetaFunction}
 */
export const meta = () => {
  return [{title: 'Profile | Glove Box Australia Pty Ltd'}];
};

/**
 * @param {LoaderFunctionArgs}
 */
export async function loader({context}) {
  await context.customerAccount.handleAuthStatus();

  return json({});
}

/**
 * @param {ActionFunctionArgs}
 */
export async function action({request, context}) {
  const {customerAccount} = context;

  if (request.method !== 'PUT') {
    return json({error: 'Method not allowed'}, {status: 405});
  }

  const form = await request.formData();

  try {
    const fullData = {};
    const inputData = {};
    const validInputKeys = ['firstName', 'lastName'];
    for (const [key, value] of form.entries()) {
      if (typeof value === 'string' && value.length) {
        fullData[key] = value;

        if (validInputKeys.includes(key)) {
          inputData[key] = value;
        }
      }
    }

    const {data: detailsUpdate, errors: detailsError} =
      await customerAccount.mutate(CUSTOMER_UPDATE_MUTATION, {
        variables: {
          customer: inputData,
        },
      });

    const metafields = [
      {
        namespace: 'custom',
        key: 'phone',
        value: fullData.phone,
        ownerId: fullData.id,
      },
      {
        namespace: 'custom',
        key: 'company',
        value: fullData.company,
        ownerId: fullData.id,
      },
    ];
    if (fullData.description) {
      metafields.push({
        namespace: 'custom',
        key: 'description',
        value: fullData.description,
        ownerId: fullData.id,
      });
    }
    if (!fullData.status) {
      metafields.push({
        namespace: 'custom',
        key: 'status',
        value: 'Pending',
        ownerId: fullData.id,
      });
    }

    const {data: metafieldsUpdate, errors: metafieldsError} =
      await customerAccount.mutate(CUSTOMER_UPDATE_METAFIELDS_MUTATION, {
        variables: {
          metafields: metafields,
        },
      });

    if (detailsError) {
      throw new Error(detailsError);
    }

    if (metafieldsError) {
      throw new Error(metafieldsError);
    }

    return json({
      customer: detailsUpdate?.customerUpdate?.customer,
      inputMetafields: metafields,
      metafieldsResult: metafieldsUpdate,
      errors1: detailsError,
      errors2: metafieldsError,
      fullData,
    });
  } catch (error) {
    return json(
      {error: error.message, customer: null},
      {
        status: 400,
      },
    );
  }
}

export default function AccountProfile() {
  const {customer} = useOutletContext();

  return (
    <Suspense fallback={<Loading />}>
      <Await resolve={customer}>
        {({status, details}) => {
          return (
            <div className="account-profile">
              <div className="flex flex-col">
                <h1 className="text-2xl">
                  {details.firstName
                    ? `Welcome, ${details.firstName}`
                    : 'Welcome to your account'}
                </h1>
                <WelcomeMessage status={status} />
              </div>
              <EditProfileForm customerDetails={details} />
            </div>
          );
        }}
      </Await>
    </Suspense>
  );
}

function WelcomeMessage({status: {isPending, isApproved}}) {
  if (isApproved) {
    return <Message.Approved />;
  }
  if (isPending) {
    return <Message.Pending />;
  }

  return <Message.New onProfilePage />;
}

/**
 * @typedef {{
 *   error: string | null;
 *   customer: CustomerFragment | null;
 * }} ActionResponse
 */

/** @typedef {import('customer-accountapi.generated').CustomerFragment} CustomerFragment */
/** @typedef {import('@shopify/hydrogen/customer-account-api-types').CustomerUpdateInput} CustomerUpdateInput */
/** @typedef {import('@shopify/remix-oxygen').ActionFunctionArgs} ActionFunctionArgs */
/** @typedef {import('@shopify/remix-oxygen').LoaderFunctionArgs} LoaderFunctionArgs */
/** @template T @typedef {import('@remix-run/react').MetaFunction<T>} MetaFunction */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof action>} ActionReturnData */
