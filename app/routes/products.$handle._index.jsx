import {redirect} from '@shopify/remix-oxygen';
import {json} from '@shopify/remix-oxygen';

export async function loader({params, request}) {
  const url = new URL(request.url);
  const searchParams = url.searchParams;
  const paramsString =
    searchParams.size > 0 ? `?${searchParams.toString()}` : '';
  return redirect(`/products/${params.handle}/description${paramsString}`);
}

/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
