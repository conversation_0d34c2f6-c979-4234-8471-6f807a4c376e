import {defer} from '@shopify/remix-oxygen';
import {
  Await,
  useLoaderData,
  useRouteLoaderData,
  Form,
  useSearchParams,
  Link,
} from '@remix-run/react';
import {motion} from 'motion/react';
import {Suspense, useState, useId} from 'react';
import {Image, Money} from '@shopify/hydrogen';
import {METAOBJECTS_QUERY} from '~/lib/fragments';
import {PaginatedResourceSection} from '~/components/PaginatedResourceSection';
import * as Message from '~/components/AccountMessage';
import {Loading} from '~/components/Loading';
import {
  SEARCH_ENDPOINT,
  SearchFormPredictive,
} from '~/components/SearchFormPredictive';
import {
  getEmptyPredictiveSearchResult,
  urlWithTrackingParams,
} from '~/lib/search';

import {HomeSidebar} from '~/components/layout/sidebar/home-sidebar';
import {SearchResultsPredictive} from '~/components/SearchResultsPredictive';
import {SearchBar} from '~/components/SearchBar';
import {ImageCard} from '~/components/image-card';
import {ProductPrice} from '~/components/ProductPrice';
import {cn, getMenuUrl} from '~/lib/utils';
import {Badge} from '~/components/ui/badge';
import backgroundMolecule from '~/assets/background_molecule.svg';

/**
 * @type {MetaFunction}
 */
export const meta = () => {
  return [{title: 'Home | Glove Box Australia Pty Ltd'}];
};

/**
 * @param {LoaderFunctionArgs} args
 */
export async function loader(args) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return defer({...deferredData, ...criticalData});
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 * @param {LoaderFunctionArgs}
 */
async function loadCriticalData({context, params, request}) {
  const specials = await context.storefront.query(SPECIALS_QUERY, {
    cache: context.storefront.CacheLong(),
    variables: {},
  });

  return {
    specialsCollection: specials.collectionByHandle,
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 * @param {LoaderFunctionArgs}
 */
function loadDeferredData({context}) {
  return {};
}

function Header({children}) {
  return (
    <h2 className="text-center text-2xl md:text-3xl font-bold underline p-2">
      {children}
    </h2>
  );
}

export default function Homepage() {
  const {mainMenu, customer} = useRouteLoaderData('root');
  const {shop, menu} = mainMenu;
  /** @type {LoaderReturnData} */
  const {specialsCollection} = useLoaderData();
  //console.log('specialsCollection', specialsCollection);
  const queriesDatalistId = useId();
  const [firstProduct, ...restProducts] = specialsCollection?.products?.nodes;

  return (
    <>
      <div className="contents md:grid md:grid-cols-12 md:gap-sides">
        <HomeSidebar menu={menu} />
        <div
          id="content"
          className={cn(
            'w-full flex relative flex-col col-span-8',
            ' bg-gba-dark-blue',
          )}
        >
          <div
            className="absolute size-full opacity-20"
            style={{
              backgroundImage: `url(${backgroundMolecule})`,
              backgroundSize: 'contain',
              backgroundPosition: '0% 0%',
              backgroundRepeat: 'repeat-y',
            }}
          ></div>
          <div className="relative h-[50vh] w-full flex items-center">
            <div className="h-full px-12 flex flex-col justify-center gap-4 text-white font-semibold">
              <p className="text-2xl">LIMITED TIME ONLY!</p>
              <p className="text-4xl text-gba-orange font-semibold text-shadow-lg">
                End of Calendar Year Promotion
              </p>
              <p className="text-2xl">
                Special prices for Magnetics Stirrers, Rotary Operators, Fume
                Hoods
              </p>
              <Link
                prefetch="intent"
                to="/account"
                className="bg-gba-orange rounded-xl py-2 px-4 w-fit text-white font-bold"
              >
                SHOP NOW
              </Link>
            </div>
            <img
              src="https://cdn.shopify.com/s/files/1/0631/6621/8321/collections/Glove_Box_Australia_-_Magnetic_Stirrers.webp?v=**********"
              alt="lab equipment"
              className="h-full"
            />
          </div>

          <div className="sticky top-header left-0 z-10 w-full pointer-events-none base-grid">
            <div className="p-4 hidden lg:block absolute top-0">
              <Badge variant="gba-blue" className="text-xl">
                CURRENT PROMOTIONS
              </Badge>
            </div>
          </div>

          <div className="relative grid grid-cols-3 p-2 gap-2 w-full">
            {specialsCollection?.products?.nodes.map((product, idx) => {
              const variant = product?.variants?.nodes?.[0];

              return (
                <ImageCard
                  key={product.id}
                  to={`/products/${product.handle}`}
                  className="relative rounded"
                  imageData={product?.featuredImage}
                  customContent={true}
                >
                  <div
                    className={cn(
                      'absolute bottom-0 right-0 m-2 p-2 flex flex-col rounded',
                      'bg-primary text-white shadow',
                    )}
                  >
                    {/* <Badge className="w-fit">Best Seller</Badge> */}
                    <div className="flex items-center flex-wrap">
                      <h2>{product.title}</h2>
                    </div>
                  </div>
                </ImageCard>
              );
            })}
          </div>
        </div>
      </div>
      {/* <motion.div
        initial={{opacity: 0, y: -50}}
        whileInView={{opacity: 1, y: 0}}
        className="flex flex-col items-center text-center bg-gba-blue text-white"
      >
        <div className="flex flex-col rounded-2xl gap-1 md:text-xl p-4 mx-4">
          <Suspense fallback={<Loading />}>
            <Await resolve={customer}>
              {({status: {isLoggedIn, isApproved, isPending}}) => {
                if (!isLoggedIn) {
                  return <Message.LoggedOut />;
                }

                if (isApproved) {
                  return <Message.Approved />;
                }

                if (isPending) {
                  return <Message.Pending />;
                }

                return <Message.New />;
              }}
            </Await>
          </Suspense>
        </div>
      </motion.div> */}
      <br />

      <div className="flex flex-col items-center p-4 bg-[#fcfcfc]">
        <Header>Specials</Header>
        <div className="products-grid">
          {specialsCollection?.products?.nodes.map((product) => {
            const variant = product?.variants?.nodes?.[0];

            return (
              <ImageCard
                key={product.id}
                to={`/products/${product.handle}`}
                title={product.title}
                titleClassName="text-sm"
                imageData={product?.featuredImage}
                onSale={true}
              >
                <small className="text-lg">
                  <Suspense fallback={<Loading />}>
                    <Await resolve={customer}>
                      {({status: {isLoggedIn, isApproved}}) =>
                        isLoggedIn &&
                        isApproved && (
                          <ProductPrice
                            price={variant?.price}
                            compareAtPrice={variant?.compareAtPrice}
                          />
                        )
                      }
                    </Await>
                  </Suspense>
                </small>
              </ImageCard>
            );
          })}
        </div>
        <Link to="/collections/specials">View More</Link>
      </div>

      <motion.div
        initial={{opacity: 0, y: -20}}
        whileInView={{opacity: 1, y: 0}}
        className="flex flex-col items-center p-8"
      >
        <Header>Quick search:</Header>
        <br />
        <SearchFormPredictive className="flex flex-col px-2">
          {({fetchResults, goToSearch, inputRef}) => (
            <SearchBar
              name="q"
              onChange={fetchResults}
              onFocus={fetchResults}
              ref={inputRef}
              placeholder="Search for products (title, sku, tag) or categories"
              type="search"
              className="pl-10 shadow-lg"
              list={queriesDatalistId}
              containerClassName="w-[330px] md:w-[400px]"
            />
          )}
        </SearchFormPredictive>
        <SearchResultsPredictive>
          {({items, total, term, state, closeSearch}) => {
            const {articles, collections, pages, products, queries} = items;

            if (state === 'loading' && term.current) {
              return <Loading />;
            }

            if (!total) {
              return <SearchResultsPredictive.Empty term={term} />;
            }

            return (
              term.current && (
                <>
                  <SearchResultsPredictive.Queries
                    queries={queries}
                    queriesDatalistId={queriesDatalistId}
                  />
                  {!!products.length && (
                    <div>
                      <p className="font-medium text-lg p-4">Products</p>
                      <div className="products-grid">
                        {products.map((product) => {
                          const productUrl = urlWithTrackingParams({
                            baseUrl: `/products/${product.handle}`,
                            trackingParams: product.trackingParameters,
                            term: term.current,
                          });
                          const variant = product?.variants?.nodes?.[0];
                          const onSale = product?.tags.includes('Specials');

                          return (
                            <ImageCard
                              key={product.id}
                              to={productUrl}
                              title={product.title}
                              titleClassName="text-sm"
                              imageData={variant?.image}
                              onSale={onSale}
                            >
                              <small className="text-lg">
                                <Suspense fallback={<Loading />}>
                                  <Await resolve={customer}>
                                    {({status: {isLoggedIn, isApproved}}) =>
                                      isLoggedIn &&
                                      isApproved && (
                                        <ProductPrice
                                          price={variant?.price}
                                          compareAtPrice={
                                            variant?.compareAtPrice
                                          }
                                        />
                                      )
                                    }
                                  </Await>
                                </Suspense>
                              </small>
                            </ImageCard>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {!!collections.length && (
                    <div>
                      <p className="font-medium text-lg p-4">Categories</p>
                      <div className="products-grid">
                        {collections.map((collection) => {
                          const url =
                            collection?.alternateLink?.value ||
                            `/categories/${collection.handle}`;
                          return (
                            <ImageCard
                              key={collection.id}
                              to={url}
                              title={collection.title}
                              titleClassName="text-sm"
                              imageData={collection.image}
                            />
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {term.current && total ? (
                    <Link
                      onClick={closeSearch}
                      to={`${SEARCH_ENDPOINT}?q=${term.current}`}
                    >
                      <p>
                        View all results for <q>{term.current}</q>
                        &nbsp; →
                      </p>
                    </Link>
                  ) : null}
                </>
              )
            );
          }}
        </SearchResultsPredictive>
        <br />
      </motion.div>
    </>
  );
}

const SPECIALS_QUERY = `#graphql
  query getSpecialsCollection($country: CountryCode, $language: LanguageCode)
  @inContext(country: $country, language: $language) {
    collectionByHandle(handle: "specials") {
      id
      title
      products(first: 12) {
        nodes {
          id
          handle
          title
          featuredImage {
            id
            altText
            url
            width
            height
          }
          variants(first: 1) {
            nodes {
              price {
                amount
                currencyCode
              }
              compareAtPrice {
                amount
                currencyCode
              }
              selectedOptions {
                name
                value
              }
            }
          }
        }
      }
    }
  }
`;

const COLLECTION_QUERY = `#graphql
  query getCollectionFromHandle($country: CountryCode, $language: LanguageCode, $handle: String!) 
  @inContext(country: $country, language: $language) {
    collectionByHandle(handle: $handle) {
      id
      title
      image {
        id
        url
        altText
        width
        height
      }
      handle
    }
  }
`;

const FEATURED_COLLECTION_QUERY = `#graphql
  fragment FeaturedCollection on Collection {
    id
    title
    image {
      id
      url
      altText
      width
      height
    }
    handle
  }
  query FeaturedCollection($country: CountryCode, $language: LanguageCode)
    @inContext(country: $country, language: $language) {
    collections(first: 5, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...FeaturedCollection
      }
    }
  }
`;

const RECOMMENDED_PRODUCTS_QUERY = `#graphql
  fragment RecommendedProduct on Product {
    id
    title
    handle
    priceRange {
      minVariantPrice {
        amount
        currencyCode
      }
    }
    images(first: 1) {
      nodes {
        id
        url
        altText
        width
        height
      }
    }
  }
  query RecommendedProducts ($country: CountryCode, $language: LanguageCode)
    @inContext(country: $country, language: $language) {
    products(first: 4, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...RecommendedProduct
      }
    }
  }
`;

/** @typedef {import('@shopify/remix-oxygen').LoaderFunctionArgs} LoaderFunctionArgs */
/** @template T @typedef {import('@remix-run/react').MetaFunction<T>} MetaFunction */
/** @typedef {import('storefrontapi.generated').FeaturedCollectionFragment} FeaturedCollectionFragment */
/** @typedef {import('storefrontapi.generated').RecommendedProductsQuery} RecommendedProductsQuery */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
/**
 * @typedef {Object} HeaderProps
 * @property {HeaderQuery} header
 * @property {Promise<CartApiQueryFragment|null>} cart
 * @property {Promise<boolean>} isLoggedIn
 * @property {string} publicStoreDomain
 */
