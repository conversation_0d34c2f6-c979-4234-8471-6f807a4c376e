import {useOutletContext} from '@remix-run/react';
import iconDownload from '~/assets/download.svg';

export default function ProductDownloads() {
  const {product} = useOutletContext();
  const {downloads} = product;
  const {value: dlString} = downloads;
  const dls = JSON.parse(dlString);

  return (
    <div>
      <br />
      {dls.map(({text, url}) => {
        return (
          <a key={url} href={url} target="_blank" className="flex flex-row">
            <img src={iconDownload} alt="Download" />
            <p className="m-2">{text}</p>
          </a>
        );
      })}
    </div>
  );
}
