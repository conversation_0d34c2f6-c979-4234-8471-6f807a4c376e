import { redirect } from '@shopify/remix-oxygen';

// if we dont implement this, /account/logout will get caught by account.$.tsx to do login

export async function loader() {
  return redirect('/');
}

/**
 * @param {ActionFunctionArgs}
 */
export async function action({ request, context }) {
  // https://shopify.dev/docs/api/hydrogen/2024-10/utilities/createhydrogencontext#createhydrogencontextoptions-propertydetail-customeraccount
  // Note that the full URI, including the tail '/' must be added to the customer admin settings.
  const postLogoutRedirectUri = '/';
  return context.customerAccount.logout({postLogoutRedirectUri});
}

/** @typedef {import('@shopify/remix-oxygen').ActionFunctionArgs} ActionFunctionArgs */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof action>} ActionReturnData */
