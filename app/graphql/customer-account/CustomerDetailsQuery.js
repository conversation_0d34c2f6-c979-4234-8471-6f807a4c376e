// NOTE: https://shopify.dev/docs/api/customer/latest/objects/Customer
export const CUSTOMER_FRAGMENT = `#graphql
  fragment Customer on Customer {
    id
    firstName
    lastName
    emailAddress {
      emailAddress
    }
    tags
    metafields(identifiers: [{namespace: "custom", key: "status"}, 
    {namespace: "custom", key: "company"},
    {namespace: "custom", key: "phone"}, 
    {namespace: "custom", key: "description"}]) {
      key  
      value
    }
    defaultAddress {
      ...Address
    }
    addresses(first: 6) {
      nodes {
        ...Address
      }
    }
  }
  fragment Address on CustomerAddress {
    id
    formatted
    firstName
    lastName
    company
    address1
    address2
    territoryCode
    zoneCode
    city
    zip
    phoneNumber
  }
`;

// NOTE: https://shopify.dev/docs/api/customer/latest/queries/customer
export const CUSTOMER_DETAILS_QUERY = `#graphql
  query CustomerDetails {
    customer {
      ...Customer
    }
  }
  ${CUSTOMER_FRAGMENT}
`;

export const CUSTOMER_STATUS_QUERY = `#graphql
  query CustomerStatus {
    customer {
      metafield(namespace: "custom", key: "status") {
        value
      }
    }
  }
`;
