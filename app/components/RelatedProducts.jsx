import {Money} from '@shopify/hydrogen';
import {ImageCard} from '~/components/image-card';

export function RelatedProducts({product}) {
  const products = product?.relatedProducts?.references?.nodes;

  return products ? (
    <div className="recommended-products p-2">
      <h2 className="text-xl font-bold p-2">Related Products</h2>
      <div className="recommended-products-grid">
        {products.map((product) => {
          return (
            <ImageCard
              key={product.id}
              to={`/products/${product.handle}`}
              title={product.title}
              imageData={product.images.nodes[0]}
            >
              <small>
                {/* <Money data={product.priceRange.minVariantPrice} /> */}
              </small>
            </ImageCard>
          );
        })}
      </div>
      <br />
    </div>
  ) : null;
}
