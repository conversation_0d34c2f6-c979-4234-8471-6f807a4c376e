import {Fragment} from 'react';
import {NavLink} from '@remix-run/react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '~/components/ui/breadcrumb';
import {getMenuUrl} from '~/lib/utils';

export function Breadcrumbs({breadcrumbs}) {
  if (breadcrumbs.length < 2) return null;

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs &&
          breadcrumbs.map((menuItem, index) => {
            return (
              <Fragment key={index}>
                <BreadcrumbItem>
                  {index < breadcrumbs.length - 1 ? (
                    <BreadcrumbLink asChild>
                      <NavLink end to={getMenuUrl(menuItem)}>
                        {menuItem?.title}
                      </NavLink>
                    </BreadcrumbLink>
                  ) : (
                    <BreadcrumbPage>{menuItem?.title}</BreadcrumbPage>
                  )}
                </BreadcrumbItem>

                {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
              </Fragment>
            );
          })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

function activeLinkStyle({isActive, isPending}) {
  return {
    fontWeight: isActive ? 'bold' : undefined,
    color: isPending ? 'grey' : 'white',
  };
}
