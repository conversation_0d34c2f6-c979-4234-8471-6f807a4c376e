import {useNavigate} from '@remix-run/react';
import {VariantSelector} from '@shopify/hydrogen';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';

/**
 * @param {{
 *   product: ProductFragment;
 *   selectedVariant: ProductFragment['selectedVariant'];
 *   variants: Array<ProductVariantFragment>;
 * }}
 */
export function ProductVariantSelector({product, selectedVariant, variants}) {
  return (
    <VariantSelector
      handle={product.handle}
      options={product.options.filter(
        (option) => option.optionValues.length > 1,
      )}
      variants={variants}
    >
      {({option}) => <ProductOptions key={option.name} option={option} />}
    </VariantSelector>
  );
}

/**
 * @param {{option: VariantOption}}
 */
function ProductOptions({option}) {
  const navigate = useNavigate();

  return (
    <div className="product-options" key={option.name}>
      <h5 className="p-1">{option.name}:</h5>
      <Select
        value={option.value}
        onValueChange={(name) => {
          const selectedOption = option.values.find(
            (value) => value.value === name,
          );
          navigate(selectedOption.to, {
            replace: true,
            preventScrollReset: true,
          });
        }}
      >
        <SelectTrigger className="max-w-[250px] bg-white shadow">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {option.values.map(({value, isAvailable, isActive, to}) => {
            return (
              <SelectItem key={option.name + value} value={value}>
                {value}
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
      {/* <div className="product-options-grid">
        {option.values.map(({value, isAvailable, isActive, to}) => {
          return (
            <Link
              className="product-options-item"
              key={option.name + value}
              prefetch="intent"
              preventScrollReset
              replace
              to={to}
              style={{
                border: isActive
                  ? '2px solid #25AADD'
                  : '1px solid transparent',
                opacity: isAvailable ? 1 : 0.3,
              }}
            >
              {value}
            </Link>
          );
        })}
      </div> */}
      <br />
    </div>
  );
}

/** @typedef {import('@shopify/hydrogen').VariantOption} VariantOption */
/** @typedef {import('storefrontapi.generated').ProductFragment} ProductFragment */
/** @typedef {import('storefrontapi.generated').ProductVariantFragment} ProductVariantFragment */
