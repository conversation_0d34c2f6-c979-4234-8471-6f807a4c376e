import {useState, useEffect} from 'react';
import {Image} from '@shopify/hydrogen';
import {Loading} from '~/components/Loading';
import {SaleBanner} from '~/components/SaleBanner';
import {cn} from '~/lib/utils';

/**
 * @param {{
 *   image: ProductVariantFragment['image'];
 * }}
 */
export function ProductImage({product, className}) {
  const [selectedIdx, setSelectedIdx] = useState(0);
  const [selectedImage, setSelectedImage] = useState(null);

  const allImages = product?.images?.nodes;
  const variant = product?.selectedVariant;
  const onSale = variant?.product?.tags.includes('Specials');

  useEffect(() => {
    if (allImages && selectedIdx > 0) {
      setSelectedImage(allImages[selectedIdx]);
    }

    if (selectedIdx === 0) {
      setSelectedImage(variant?.image);
    }
  }, [selectedIdx, allImages]);

  return (
    <>
      <div className="relative w-[300px] md:w-[450px] aspect-square product-image">
        {onSale && <SaleBanner />}
        {selectedImage && <ImageMagnifier image={selectedImage} />}
      </div>
      {allImages?.length > 1 && (
        <ImageSelector
          selectedImage={selectedImage}
          setSelectedImage={setSelectedImage}
          images={allImages}
        />
      )}
    </>
  );
}

function ImageMagnifier({image}) {
  const [position, setPosition] = useState({x: 0, y: 0});
  const [showMagnifier, setShowMagnifier] = useState(false);
  const small = {
    ...image,
    url: image.smallUrl,
  };
  const big = {
    ...image,
    url: image.bigUrl,
  };

  const onMouseHover = (e) => {
    const {top, left, width, height} = e.target.getBoundingClientRect();
    const x = ((e.pageX - left) / width) * 100;
    const y = ((e.pageY - top) / height) * 100;
    setPosition({x, y});
  };

  return (
    <div
      className={`relative size-full overflow-hidden round shadow-lg ${showMagnifier ? 'hover:cursor-zoom-out' : 'hover:cursor-zoom-in'}`}
      onClick={() => setShowMagnifier(!showMagnifier)}
      onMouseLeave={() => setShowMagnifier(false)}
      onMouseMove={onMouseHover}
    >
      <Image
        alt={small.altText || 'Product Image'}
        data={small}
        aspectRatio="1/1"
        sizes="(min-width: 45em) 50vw, 100vw"
        className="round shadow-lg"
      />
      <div
        className={cn(
          'absolute top-0 left-0 size-full pointer-events-none bg-white',
          showMagnifier ? 'opacity-100' : 'opacity-0',
        )}
      >
        <div
          style={{
            backgroundImage: `url(${big.url})`,
            backgroundPosition: `${position.x}% ${position.y}%`,
          }}
          className="size-full"
        ></div>
      </div>
    </div>
  );
}

function ImageSelector({selectedImage, setSelectedImage, images}) {
  return (
    <div className="mt-2 flex gap-2">
      {images.map((image, idx) => (
        <button
          key={image.id}
          onClick={() => setSelectedImage(image)}
          className="flex-1"
        >
          <Image
            alt={image.altText || 'Product Image'}
            data={image}
            aspectRatio="1/1"
            sizes="(min-width: 45em) 50vw, 100vw"
            className={`h-[55px] round shadow ${selectedImage?.id === image.id ? 'opacity-100' : 'opacity-30'}`}
          />
        </button>
      ))}
    </div>
  );
}

/** @typedef {import('storefrontapi.generated').ProductVariantFragment} ProductVariantFragment */
