import {Pagination} from '@shopify/hydrogen';
import {Loading} from '~/components/Loading';
import {useInView} from 'react-intersection-observer';
import {InfiniteScroll} from '~/components/InfiniteScroll';

/**
 * <PaginatedResourceSection > is a component that encapsulate how the previous and next behaviors throughout your application.
 * @param {Class<Pagination<NodesType>>['connection']>}
 */

export function PaginatedResourceSection({
  connection,
  children,
  resourcesClassName,
}) {
  const {ref, inView, entry} = useInView();

  return (
    <Pagination connection={connection}>
      {({
        nodes,
        isLoading,
        PreviousLink,
        NextLink,
        hasNextPage,
        nextPageUrl,
        state,
      }) => {
        const resourcesMarkup = nodes.map((node, index) =>
          children({node, index}),
        );

        return (
          <div>
            <PreviousLink>
              {isLoading ? <Loading /> : <span>↑ Load previous</span>}
            </PreviousLink>
            <InfiniteScroll
              className={resourcesClassName}
              inView={inView}
              hasNextPage={hasNextPage}
              nextPageUrl={nextPageUrl}
              state={state}
            >
              {resourcesMarkup}
            </InfiniteScroll>
            <NextLink ref={ref}>
              {isLoading ? <Loading /> : <p className="p-4">Load more ↓</p>}
            </NextLink>
          </div>
        );
      }}
    </Pagination>
  );
}
