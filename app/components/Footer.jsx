import {Suspense} from 'react';
import {<PERSON>wait, Link, NavLink} from '@remix-run/react';
import {Image} from '@shopify/hydrogen';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarShortcut,
  MenubarTrigger,
  MenubarSub,
  MenubarSubTrigger,
  MenubarSubContent,
} from '~/components/ui/menubar';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '~/components/ui/carousel';
import Autoplay from 'embla-carousel-autoplay';
import {getRelativeUrl} from '~/lib/utils';
import iconEmail from '~/assets/email.svg';
import iconPhone from '~/assets/phone.svg';
import {Loading} from '~/components/Loading';
import {getMenuUrl} from '~/lib/utils';

/**
 * @param {FooterProps}
 */
export function Footer({mainMenu, footerMenu, brands, catalogues, socials}) {
  return (
    <div className="footer">
      <Suspense fallback={<Loading />}>
        <Await resolve={catalogues}>
          {(catalogues) => {
            return (
              <div className="section-catalogues pb-8">
                <p className="px-2 py-4 font-medium text-gba-blue text-xl md:text-2xl uppercase">
                  Download our catalogues:
                </p>
                <div className="flex flex-wrap justify-center gap-4 md:gap-12">
                  {catalogues?.map((catalogue) => (
                    <a
                      key={catalogue.id}
                      href={catalogue.link_url}
                      target="_blank"
                    >
                      <img
                        src={catalogue.url}
                        className="h-[222px] w-full object-fit"
                      />
                    </a>
                  ))}
                </div>
              </div>
            );
          }}
        </Await>
      </Suspense>
      <div className="section-socials p-4">
        <Suspense fallback={<Loading />}>
          <Await resolve={socials}>
            {(socials) => (
              <ul className="flex gap-4 justify-end">
                {socials.map((item) => {
                  return (
                    <Link
                      key={item.imageId}
                      to={item.linkUrl}
                      className="h-[25px]"
                    >
                      <Image
                        data={item.image}
                        sizes="(min-width: 45em) 20vw, 100vw"
                        alt={item.title}
                        className="h-full w-auto"
                      />
                    </Link>
                  );
                })}
              </ul>
            )}
          </Await>
        </Suspense>
      </div>

      <div className="section-details grid md:grid-cols-3 gap-4 grid-flow-row p-4">
        <div className="flex flex-col md:items-start gap-1 p-2 text-sm">
          <p className="font-medium text-lg">Menu</p>
          {mainMenu?.menu?.items.map((category) => {
            return (
              <NavLink key={category.id} to={getMenuUrl(category)}>
                {category.title}
              </NavLink>
            );
          })}
        </div>
        <Suspense fallback={'Loading brands...'}>
          <Await resolve={brands}>
            {(brands) => {
              return (
                <div className="flex flex-col items-center">
                  <p className="my-2 font-medium text-2xl">
                    Proudly represent:
                  </p>
                  <Carousel
                    className="max-w-[250px] md:max-w-[350px]"
                    opts={{loop: true}}
                    plugins={[
                      Autoplay({
                        delay: 3000,
                      }),
                    ]}
                  >
                    <CarouselContent>
                      {brands?.map((brand) => (
                        <CarouselItem
                          key={brand.id}
                          className="basis-full flex justify-center items-center"
                        >
                          <img
                            src={brand.url}
                            className="max-w-auto h-[60px] md:h-[80px]"
                          />
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious />
                    <CarouselNext />
                  </Carousel>
                  <p className="mt-2 text-sm">
                    In Australia, New Zealand and all other countries in Oceania
                  </p>
                </div>
              );
            }}
          </Await>
        </Suspense>
        <div className="flex flex-col md:items-end gap-1 p-2  text-sm">
          <p className="font-medium text-lg">Glove Box Australia Pty Ltd</p>
          <div>
            Suite 16120, 440 Collins Street, Melbourne, VIC 3000, Australia
          </div>
          <div>PO Box 16120, 440 Collins Street, Melbourne VIC 8000</div>
          <div>ABN: 40 ***********</div>
          <div className="flex items-center gap-1">
            <img src={iconEmail} alt="email" className="w-[22px]" />
            <a href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </div>
          <div className="flex items-center gap-1">
            <img src={iconPhone} alt="phone" className="w-[22px]" />
            <span>+61 3 9193 1137</span>
          </div>
        </div>
      </div>
      <FooterMenu menu={footerMenu.menu} />
    </div>
  );
}

/**
 * @param {{
 *   menu: FooterQuery['menu'];
 *   primaryDomainUrl: FooterProps['header']['shop']['primaryDomain']['url'];
 *   publicStoreDomain: string;
 * }}
 */
function FooterMenu({menu}) {
  return (
    <nav
      className="footer-menu flex items-center p-2 text-white"
      role="navigation"
    >
      <p className="mr-auto text-xs md:text-sm">
        Copyright © 2025, Glove Box Australia Pty Ltd
      </p>
      <div className="ml-auto text-xs md:text-sm flex gap-2">
        {(menu || FALLBACK_FOOTER_MENU).items.map((item) => {
          if (!item.url) return null;
          // if the url is internal, we strip the domain
          const isExternal = !item.url.startsWith('/');
          return isExternal ? (
            <a
              href={item.url}
              key={item.id}
              rel="noopener noreferrer"
              target="_blank"
            >
              {item.title}
            </a>
          ) : (
            <NavLink
              end
              key={item.id}
              prefetch="intent"
              style={activeLinkStyle}
              to={getMenuUrl(item)}
            >
              {item.title}
            </NavLink>
          );
        })}
      </div>
    </nav>
  );
}

const FALLBACK_FOOTER_MENU = {
  id: 'gid://shopify/Menu/199655620664',
  items: [
    {
      id: 'gid://shopify/MenuItem/461633060920',
      resourceId: 'gid://shopify/ShopPolicy/23358046264',
      tags: [],
      title: 'Privacy Policy',
      type: 'SHOP_POLICY',
      url: '/policies/privacy-policy',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461633093688',
      resourceId: 'gid://shopify/ShopPolicy/23358013496',
      tags: [],
      title: 'Refund Policy',
      type: 'SHOP_POLICY',
      url: '/policies/refund-policy',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461633126456',
      resourceId: 'gid://shopify/ShopPolicy/23358111800',
      tags: [],
      title: 'Shipping Policy',
      type: 'SHOP_POLICY',
      url: '/policies/shipping-policy',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461633159224',
      resourceId: 'gid://shopify/ShopPolicy/23358079032',
      tags: [],
      title: 'Terms of Service',
      type: 'SHOP_POLICY',
      url: '/policies/terms-of-service',
      items: [],
    },
  ],
};

/**
 * @param {{
 *   isActive: boolean;
 *   isPending: boolean;
 * }}
 */
function activeLinkStyle({isActive, isPending}) {
  return {
    fontWeight: isActive ? 'bold' : undefined,
    color: isPending ? 'grey' : 'white',
  };
}

/**
 * @typedef {Object} FooterProps
 * @property {Promise<FooterQuery|null>} footer
 * @property {HeaderQuery} header
 * @property {string} publicStoreDomain
 */

/** @typedef {import('storefrontapi.generated').FooterQuery} FooterQuery */
/** @typedef {import('storefrontapi.generated').HeaderQuery} HeaderQuery */
