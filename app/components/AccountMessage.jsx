import {Link} from '@remix-run/react';

export function LoggedOut() {
  return (
    <>
      <p>
        To view prices and best deals, please{' '}
        <Link to="/account" className="text-gba-orange">
          login
        </Link>{' '}
        to your account.
      </p>
      <p>
        Logging in ensures that you have access to the most accurate and
        up-to-date pricing, along with exclusive offers tailored to your needs.
      </p>
      <p>If you don't have an account yet, registering is quick and easy!</p>
    </>
  );
}

export function Approved() {
  return (
    <div className="">
      <p className="p-1">
        Your account is <span className="text-gba-orange">approved.</span> You
        will now:
      </p>
      <ApprovedInfo />
    </div>
  );
}

export function Pending() {
  return (
    <>
      <p>
        Thank you for filling in your details. Our team is currently reviewing
        your account.
      </p>
      <p>You will be notified by email once it is approved.</p>
    </>
  );
}

export function New({onProfilePage}) {
  return (
    <div className="flex flex-col gap-2">
      <p>
        To view prices and start shopping, please request account approval by
        filling out your profile details{' '}
        {onProfilePage ? (
          'below.'
        ) : (
          <span>
            in the{' '}
            <Link to="/account" className="text-gba-orange">
              account page.
            </Link>
          </span>
        )}
      </p>
      {/* <p>Once approved, you will:</p>
      <ApprovedInfo /> */}
    </div>
  );
}

function ApprovedInfo() {
  return (
    <ul className="py-1 px-4">
      <li>Have access to the most accurate and up-to-date pricing</li>
      <li>Get exclusive offers tailored to your needs</li>
      <li>Be able to make orders/create quotes</li>
    </ul>
  );
}
