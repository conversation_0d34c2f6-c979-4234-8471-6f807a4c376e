import {Form, useFetcher} from '@remix-run/react';
import {Input} from '~/components/ui/input';
import {Label} from '~/components/ui/label';
import {Textarea} from '~/components/ui/textarea';
import {Button} from '~/components/ui/button';

export function EditProfileForm({customerDetails, children}) {
  const fetcher = useFetcher();
  const {state, data} = fetcher;

  return (
    <fetcher.Form
      method="put"
      action="/account/profile"
      className="grid gap-4 py-4"
    >
      <div className="hidden">
        <input
          id="id"
          name="id"
          type="text"
          value={customerDetails.id ?? ''}
          readOnly
        />
        <input
          id="status"
          name="status"
          type="text"
          value={customerDetails.status ?? ''}
          readOnly
        />
      </div>
      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          name="email"
          type="email"
          value={customerDetails.email ?? ''}
          disabled
          readOnly
          className="bg-gray-800 text-white"
        />
      </div>
      <div className="grid grid-cols-2 items-center gap-4">
        <div>
          <Label htmlFor="firstName" className="text-right">
            First name <Required />
          </Label>
          <Input
            id="firstName"
            name="firstName"
            type="text"
            autoComplete="given-name"
            placeholder="First name"
            aria-label="First name"
            defaultValue={customerDetails.firstName ?? ''}
            required
          />
        </div>
        <div>
          <Label htmlFor="lastName" className="text-right">
            Last name
          </Label>
          <Input
            id="lastName"
            name="lastName"
            type="text"
            autoComplete="family-name"
            placeholder="Last name"
            aria-label="Last name"
            defaultValue={customerDetails.lastName ?? ''}
            minLength={2}
          />
        </div>
      </div>
      <div>
        <Label htmlFor="phone">
          Phone Number <Required />
        </Label>
        <Input
          id="phone"
          name="phone"
          type="tel"
          autoComplete="phone"
          placeholder="Phone Number"
          aria-label="Phone Number"
          defaultValue={customerDetails.phone ?? ''}
          minLength={2}
          required
        />
      </div>
      <div>
        <Label htmlFor="company">
          Company <Required />
        </Label>
        <Input
          id="company"
          name="company"
          type="text"
          autoComplete="company"
          placeholder="Company"
          aria-label="Company"
          defaultValue={customerDetails.company ?? ''}
          minLength={2}
          required
        />
      </div>
      <div>
        <Label htmlFor="description">
          Description of your business/project
        </Label>
        <Textarea
          rows="4"
          id="description"
          name="description"
          type="text"
          autoComplete="description"
          placeholder="Description of your business/project"
          aria-label="Description"
          defaultValue={customerDetails.description ?? ''}
          minLength={2}
        />
      </div>
      {children}

      <Button
        type="submit"
        disabled={state !== 'idle'}
        className="bg-gba-orange w-fit"
      >
        {state !== 'idle'
          ? 'Updating'
          : customerDetails.status
            ? 'Update Profile'
            : 'Request account approval'}
      </Button>
      {data?.error && (
        <p>
          <mark>
            <small>{data.error}</small>
          </mark>
        </p>
      )}
    </fetcher.Form>
  );
}

function Required() {
  return <span className="text-red-500">*</span>;
}
