import {CartForm} from '@shopify/hydrogen';

/**
 * @param {{
 *   analytics?: unknown;
 *   children: React.ReactNode;
 *   disabled?: boolean;
 *   lines: Array<OptimisticCartLineInput>;
 *   onClick?: () => void;
 * }}
 */
export function AddToCartButton({
  analytics,
  children,
  selectedVariant,
  quantity = 1,
  minQuantity = 1,
  className,
  disabled,
  onClick,
}) {
  return (
    <CartForm
      route="/cart"
      inputs={{
        lines: selectedVariant
          ? [
              {
                attributes: [
                  {
                    key: 'moq',
                    value: minQuantity.toString(),
                  },
                ],
                merchandiseId: selectedVariant.id,
                quantity,
                selectedVariant,
              },
            ]
          : [],
      }}
      action={CartForm.ACTIONS.LinesAdd}
    >
      {(fetcher) => (
        <div className="bg-gba-orange text-white round py-2 px-4 w-fit">
          <input
            name="analytics"
            type="hidden"
            value={JSON.stringify(analytics)}
          />
          <button
            type="submit"
            onClick={onClick}
            disabled={
              disabled ??
              (!selectedVariant ||
                !selectedVariant.availableForSale ||
                fetcher.state !== 'idle')
            }
          >
            {children}
          </button>
        </div>
      )}
    </CartForm>
  );
}

/** @typedef {import('@remix-run/react').FetcherWithComponents} FetcherWithComponents */
/** @typedef {import('@shopify/hydrogen').OptimisticCartLineInput} OptimisticCartLineInput */
