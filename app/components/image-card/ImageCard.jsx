import {Suspense} from 'react';
import {Await, <PERSON>} from '@remix-run/react';
import {Image} from '@shopify/hydrogen';
import logoDefault from '~/assets/logo_default.png';
import {Loading} from '~/components/Loading';
import {SaleBanner} from '~/components/SaleBanner';
import {cn} from '~/lib/utils';

export function ImageCard({
  children,
  to,
  title,
  titleClassName,
  imageData,
  onSale = false,
  customContent = false,
  ...props
}) {
  return (
    <div className="relative" {...props}>
      <Link to={to} className="relative" prefetch="intent">
        <div className="relative rounded-xl shadow-md hover:shadow-lg overflow-hidden">
          {onSale && <SaleBanner />}
          {imageData ? (
            <Image
              data={imageData}
              sizes="(min-width: 45em) 20vw, 50vw"
              aspectRatio="1/1"
              className={cn([
                'transition-all duration-300',
                'will-change-transform scale-100 hover:scale-[1.05]',
              ])}
            />
          ) : (
            <img src={logoDefault} alt="logo" className="round aspect-square" />
          )}
        </div>
      </Link>
      {customContent ? (
        children
      ) : (
        <div className="flex flex-col items-center text-center">
          <h4 className={cn('font-medium p-2', titleClassName)}>{title}</h4>
          {children}
        </div>
      )}
    </div>
  );
}
