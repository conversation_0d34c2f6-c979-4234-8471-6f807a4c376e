import {Money} from '@shopify/hydrogen';
import {Loading} from '~/components/Loading';
import {cn} from '~/lib/utils';

/**
 * @param {{
 *   price?: MoneyV2;
 *   compareAtPrice?: MoneyV2 | null;
 * }}
 */
export function ProductPrice({
  className,
  price,
  compareAtPrice,
  state = 'idle',
}) {
  return (
    <div className={cn('product-price py-2', className)}>
      {state === 'idle' ? (
        price?.amount == 0 ? (
          <a
            href="https://www.gloveboxaustralia.com.au/contact"
            className="text-gba-blue"
          >
            Contact us for price
          </a>
        ) : compareAtPrice ? (
          <div className="product-price-on-sale">
            <s>
              <Money data={compareAtPrice} />
            </s>
            {price ? <Money data={price} /> : null}
          </div>
        ) : price ? (
          <Money data={price} />
        ) : (
          <span>&nbsp;</span>
        )
      ) : (
        <Loading />
      )}
    </div>
  );
}

/** @typedef {import('@shopify/hydrogen/storefront-api-types').MoneyV2} MoneyV2 */
