import {Await, <PERSON>} from '@remix-run/react';
import {Suspense, useId} from 'react';
import {Aside} from '~/components/Aside';
import {Footer} from '~/components/Footer';
import {Header, HeaderMenuMobile} from '~/components/Header';
import {CartMain} from '~/components/CartMain';
import {ProfileDialog} from '~/components/ProfileDialog';
import {SearchAside} from '~/components/aside/SearchAside';
import {getRelativeUrl} from '~/lib/utils';
import {Loading} from '~/components/Loading';

/**
 * @param {PageLayoutProps}
 */
export function PageLayout({
  children = null,
  publicStoreDomain,
  mainMenu,
  footerMenu,
  brands,
  catalogues,
  socials,
  customer,
  cart,
}) {
  return (
    <Aside.Provider>
      <CartAside cart={cart} />
      <SearchAside customer={customer} />
      <MobileMenuAside header={mainMenu} />
      {mainMenu && (
        <Header
          header={mainMenu}
          cart={cart}
          customer={customer}
          socials={socials}
        />
      )}
      <main className="page">
        <Suspense>
          <Await resolve={customer}>
            {({status: {isLoggedIn, isPending, isApproved}, details}) => {
              if (isLoggedIn) {
                if (!isPending && !isApproved) {
                  return <ProfileDialog customerDetails={details} />;
                }
              }
              return <></>;
            }}
          </Await>
        </Suspense>
        {children}
      </main>
      <Footer
        mainMenu={mainMenu}
        footerMenu={footerMenu}
        brands={brands}
        catalogues={catalogues}
        socials={socials}
      />
    </Aside.Provider>
  );
}

/**
 * @param {{cart: PageLayoutProps['cart']}}
 */
function CartAside({cart}) {
  return (
    <Aside type="cart" heading="Cart">
      <Suspense fallback={<p>Loading cart ...</p>}>
        <Await resolve={cart}>
          {(cart) => {
            return <CartMain cart={cart} layout="aside" />;
          }}
        </Await>
      </Suspense>
    </Aside>
  );
}

function MobileMenuAside({header, getUrl}) {
  return (
    header.menu &&
    header.shop.primaryDomain?.url && (
      <Aside type="mobile" heading="Menu" asideClassName="overflow-y-scroll">
        <HeaderMenuMobile menu={header.menu} getUrl={getUrl} />
      </Aside>
    )
  );
}

/**
 * @typedef {Object} PageLayoutProps
 * @property {Promise<CartApiQueryFragment|null>} cart
 * @property {Promise<FooterQuery|null>} footer
 * @property {HeaderQuery} header
 * @property {Promise<boolean>} isLoggedIn
 * @property {string} publicStoreDomain
 * @property {React.ReactNode} [children]
 */

/** @typedef {import('storefrontapi.generated').CartApiQueryFragment} CartApiQueryFragment */
/** @typedef {import('storefrontapi.generated').FooterQuery} FooterQuery */
/** @typedef {import('storefrontapi.generated').HeaderQuery} HeaderQuery */
