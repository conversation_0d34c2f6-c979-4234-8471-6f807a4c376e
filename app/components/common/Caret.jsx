import {ChevronDown, ChevronRight} from 'lucide-react';
import {cn} from '~/lib/utils';

export function CaretDown({active, className, ...props}) {
  return (
    <ChevronDown
      className={cn(
        `${active && 'rotate-180'} transition-transform duration-200`,
        className,
      )}
      aria-hidden="true"
      {...props}
    />
  );
}

export function CaretRight({active, className, ...props}) {
  return (
    <ChevronRight
      className={cn(
        `${active && 'rotate-180'} transition-transform duration-200`,
        className,
      )}
      aria-hidden="true"
      {...props}
    />
  );
}
