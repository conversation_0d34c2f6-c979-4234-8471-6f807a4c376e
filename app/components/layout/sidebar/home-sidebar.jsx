import {Award, DollarSign, HeadphonesIcon} from 'lucide-react';

export function HomeSidebar({collections}) {
  return (
    <aside className="max-md:hidden col-span-4 h-screen sticky top-0 p-sides pt-top-spacing flex flex-col justify-between">
      <div className="pt-12">
        <p className="text-lg leading-tight font-semibold">
          Trusted Laboratory Equipment Partner for Australia's Leading Research
          Institutions
        </p>
        <div className="mt-6 space-y-4">
          <div className="flex items-start gap-3 group">
            <div className="flex-shrink-0 mt-1">
              <Award className="h-5 w-5 text-blue-600 group-hover:text-blue-700 transition-colors" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">
                Research-Grade Certifications
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed">
                Complete documentation and certifications ensuring compliance
                with research standards and regulatory requirements.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 group">
            <div className="flex-shrink-0 mt-1">
              <DollarSign className="h-5 w-5 text-green-600 group-hover:text-green-700 transition-colors" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">
                Institutional Pricing & Procurement
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed">
                Competitive pricing structures and dedicated procurement support
                tailored for educational and research institutions.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 group">
            <div className="flex-shrink-0 mt-1">
              <HeadphonesIcon className="h-5 w-5 text-purple-600 group-hover:text-purple-700 transition-colors" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">
                Specialized Support
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed">
                Expert technical assistance and consultation for complex
                research applications and equipment needs.
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* <ShopLinks collections={collections} /> */}
      <div>
        <p>test 1</p>
        <p>test 2</p>
        <p>test 3</p>
        <p>test 4</p>
      </div>
    </aside>
  );
}
