import {Link} from '@remix-run/react';
import {Award, DollarSign, HeadphonesIcon} from 'lucide-react';
import PageLink from '~/components/common/PageLink';
import {cn, getMenuUrl} from '~/lib/utils';

import logoSquare from '~/assets/logo_square.png';

export function HomeSidebar({menu}) {
  return (
    <aside className="max-md:hidden col-span-4 h-screen sticky top-0 p-sides pt-header flex flex-col justify-between">
      <div className="">
        <Link to="/" className="flex h-24 my-4">
          <img src={logoSquare} alt="logo" className="max-h-full" />
        </Link>
        <p className="text-lg leading-tight font-semibold my-6">
          The Trusted Laboratory Equipment Partner for Australia's Leading
          Research Institutions
        </p>
        <div className="space-y-4">
          <div className="flex items-start gap-3 group">
            <div className="flex-shrink-0 mt-1">
              <Award className="h-5 w-5 text-blue-600 group-hover:text-blue-700 transition-colors" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">
                Research-Grade Certifications
              </h4>
            </div>
          </div>

          <div className="flex items-start gap-3 group">
            <div className="flex-shrink-0 mt-1">
              <DollarSign className="h-5 w-5 text-green-600 group-hover:text-green-700 transition-colors" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">
                Competitive Pricing
              </h4>
              {/* <p className="text-sm text-gray-600 leading-relaxed">
                Competitive pricing structures and dedicated procurement support
                tailored for educational and research institutions.
              </p> */}
            </div>
          </div>

          <div className="flex items-start gap-3 group">
            <div className="flex-shrink-0 mt-1">
              <HeadphonesIcon className="h-5 w-5 text-purple-600 group-hover:text-purple-700 transition-colors" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-1">
                Specialized Support
              </h4>
              {/* <p className="text-sm text-gray-600 leading-relaxed">
                Expert technical assistance and consultation for complex
                research applications and equipment needs.
              </p> */}
            </div>
          </div>
        </div>
        <p className="italic py-2">
          Create an account to access prices and exclusive offers.
        </p>
      </div>
      {/* <CategoryMenu menuItems={menu.items} /> */}
    </aside>
  );
}

function CategoryMenu({menuItems}) {
  return (
    <ul className="flex flex-col flex-wrap w-full gap-x-2">
      {menuItems.map((menuItem) => {
        return (
          <li key={menuItem.id}>
            <PageLink
              to={getMenuUrl(menuItem)}
              className={`block font-medium px-2 
                          w-fit transition`}
              prefetch="intent"
            >
              {menuItem.title}
            </PageLink>
          </li>
        );
      })}
    </ul>
  );
}

function FullProductMenu({menuItems}) {
  return (
    <ul className="flex flex-col flex-wrap h-[50vh] w-full gap-x-2">
      {menuItems.map((menuItem) => {
        return (
          <>
            <li key={menuItem.id}>
              <PageLink
                to={getMenuUrl(menuItem)}
                className={`block font-medium px-2 
                          w-fit transition`}
                prefetch="intent"
              >
                {menuItem.title}
              </PageLink>
              <ul>
                {menuItem.items.map((subMenuItem) => {
                  return (
                    <li key={subMenuItem.id}>
                      <PageLink
                        to={getMenuUrl(subMenuItem)}
                        className={`block px-4 
                                    w-fit transition`}
                        prefetch="intent"
                      >
                        {subMenuItem.title}
                      </PageLink>
                    </li>
                  );
                })}
              </ul>
            </li>
          </>
        );
      })}
    </ul>
  );
}
