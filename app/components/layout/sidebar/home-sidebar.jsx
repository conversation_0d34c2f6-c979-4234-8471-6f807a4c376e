import {Link} from '@remix-run/react';
import {Award, DollarSign, HeadphonesIcon} from 'lucide-react';

import logoSquare from '~/assets/logo_square.png';

export function HomeSidebar({menu}) {
  return (
    <aside className="max-md:hidden col-span-3 h-screen sticky top-0 p-sides pt-header flex flex-col justify-between">
      <div className="">
        <Link to="/" className="flex h-24 my-4">
          <img src={logoSquare} alt="logo" className="max-h-full" />
        </Link>
        <p className="text-lg leading-tight font-semibold text-gray-800 mt-6">
          Trusted Laboratory Equipment Partner for Australian Institutions
        </p>

        <div className="space-y-1 my-4">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <Award className="h-5 w-5 text-blue-600" />
            </div>
            <h4 className="font-medium text-gray-600">
              Research-Grade Certifications
            </h4>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
            <h4 className="font-medium text-gray-600">
              Competitive Institutional Pricing
            </h4>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <HeadphonesIcon className="h-5 w-5 text-purple-600" />
            </div>
            <h4 className="font-medium text-gray-600">
              Technical & Procurement Support
            </h4>
          </div>
        </div>

        <Link className="bg-gba-orange/50 border border-gray-500 rounded-lg p-2 text-sm">
          Login to access prices and exclusive offers
        </Link>
      </div>
    </aside>
  );
}
