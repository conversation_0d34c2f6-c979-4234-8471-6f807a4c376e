import {useState} from 'react';
import {AnimatePresence, motion} from 'motion/react';
import {Link, NavLink} from '@remix-run/react';
import PageLink from '~/components/common/PageLink';
import {Image} from '@shopify/hydrogen';
import {CaretDown, CaretRight} from '~/components/common/Caret';
import {cn, getMenuUrl} from '~/lib/utils';

export function SimpleNavbar({menu, className}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className={cn('relative pointer-events-auto', className)}>
      <ul className="flex gap-4">
        <li
          className="group flex items-center relative size-full cursor-pointer"
          onMouseEnter={() => setIsOpen(true)}
          onMouseLeave={() => setIsOpen(false)}
        >
          <span className="text-gray-500 group-hover:text-black transition">
            Products
          </span>
          <CaretDown
            active={isOpen}
            className="relative top-[1px] ml-1 h-4 w-4 text-gray-500 group-hover:text-black transition"
          />
          <AnimatePresence initial={false}>
            {isOpen && (
              <SimpleDrawer
                menuItems={menu.items}
                className="w-[850px] h-[800px] pt-2"
              />
            )}
          </AnimatePresence>
        </li>
      </ul>
    </nav>
  );
}

function SimpleDrawer({menuItems, className}) {
  return (
    <motion.div
      initial={{opacity: 0, y: 0}}
      animate={{opacity: 1, y: 0}}
      exit={{opacity: 0, y: 0}}
      className={cn('absolute top-full', className)}
    >
      <div className="flex bg-[#f3f3f3] border border-gray-400 py-4 px-2 rounded-b-xl shadow-lg h-full w-full">
        <ul className="flex flex-col flex-wrap h-full w-full gap-x-2">
          {menuItems.map((menuItem) => {
            return (
              <>
                <li key={menuItem.id}>
                  <PageLink
                    to={getMenuUrl(menuItem)}
                    className={`block font-medium px-2 
                          w-fit transition`}
                    prefetch="intent"
                  >
                    {menuItem.title}
                  </PageLink>
                  <ul>
                    {menuItem.items.map((subMenuItem) => {
                      return (
                        <li key={subMenuItem.id}>
                          <PageLink
                            to={getMenuUrl(subMenuItem)}
                            className={`block px-4 
                                    w-fit transition`}
                            prefetch="intent"
                          >
                            {subMenuItem.title}
                          </PageLink>
                        </li>
                      );
                    })}
                  </ul>
                </li>
              </>
            );
          })}
        </ul>
      </div>
    </motion.div>
  );
}
