import {useState} from 'react';
import {AnimatePresence, motion} from 'motion/react';
import {Link, NavLink} from '@remix-run/react';
import PageLink from '~/components/common/PageLink';
import {Image} from '@shopify/hydrogen';
import {CaretDown, CaretRight} from '~/components/common/Caret';
import {cn, getMenuUrl} from '~/lib/utils';

export function SimpleNavbar({menu}) {
  const [isOpen, setIsOpen] = useState(false);

  console.log(menu);
  return (
    <nav className="relative pointer-events-auto">
      <ul className="flex gap-4">
        <li
          className="group flex items-center relative size-full cursor-pointer"
          onMouseEnter={() => setIsOpen(true)}
          onMouseLeave={() => setIsOpen(false)}
        >
          <span className="group-hover:text-gba-blue transition">Products</span>
          <CaretDown
            active={isOpen}
            className="relative top-[1px] ml-1 h-4 w-4 group-hover:text-gba-blue transition"
          />
          <AnimatePresence initial={false}>
            {true && (
              <SimpleDrawer
                menuItems={menu.items}
                className="w-[400px] lg:w-[600px] h-[400px] pt-2"
              />
            )}
          </AnimatePresence>
        </li>
      </ul>
    </nav>
  );
}

function SimpleDrawer({menuItems, className}) {
  return (
    <motion.div
      initial={{opacity: 0, y: 0}}
      animate={{opacity: 1, y: 0}}
      exit={{opacity: 0, y: 0}}
      className={cn('absolute top-full', className)}
    >
      <div className="flex bg-[#e3e7eb] py-4 rounded-xl shadow-lg h-full">
        <ul className="flex flex-col flex-wrap h-full">
          {menuItems.map((menuItem) => {
            return (
              <>
                <li key={menuItem.id}>
                  <PageLink
                    to={getMenuUrl(menuItem)}
                    className={`block font-medium px-4 
                          w-fit transition`}
                    prefetch="intent"
                  >
                    {menuItem.title}
                  </PageLink>
                  <ul>
                    {menuItem.items.map((subMenuItem) => {
                      return (
                        <li key={subMenuItem.id}>
                          <PageLink
                            to={getMenuUrl(subMenuItem)}
                            className={`block px-6 
                                    w-fit transition`}
                            prefetch="intent"
                          >
                            {subMenuItem.title}
                          </PageLink>
                        </li>
                      );
                    })}
                  </ul>
                </li>
              </>
            );
          })}
        </ul>
      </div>
    </motion.div>
  );
}
