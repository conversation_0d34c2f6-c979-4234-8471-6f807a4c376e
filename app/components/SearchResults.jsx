import {Suspense} from 'react';
import {Await, <PERSON>} from '@remix-run/react';
import {Image, Pagination} from '@shopify/hydrogen';
import {urlWithTrackingParams} from '~/lib/search';
import {Loading} from '~/components/Loading';
import {ImageCard} from '~/components/image-card';
import {ProductPrice} from '~/components/ProductPrice';

/**
 * @param {Omit<SearchResultsProps, 'error' | 'type'>}
 */
export function SearchResults({term, result, children}) {
  if (!result?.total) {
    return null;
  }

  return children({...result.items, term});
}

SearchResults.Articles = SearchResultsArticles;
SearchResults.Pages = SearchResultsPages;
SearchResults.Products = SearchResultsProducts;
SearchResults.Empty = SearchResultsEmpty;

/**
 * @param {PartialSearchResult<'articles'>}
 */
function SearchResultsArticles({term, articles}) {
  if (!articles?.nodes.length) {
    return null;
  }

  return (
    <div className="search-result">
      <h2>Articles</h2>
      <div>
        {articles?.nodes?.map((article) => {
          const articleUrl = urlWithTrackingParams({
            baseUrl: `/blogs/${article.handle}`,
            trackingParams: article.trackingParameters,
            term,
          });

          return (
            <div className="search-results-item" key={article.id}>
              <Link prefetch="intent" to={articleUrl}>
                {article.title}
              </Link>
            </div>
          );
        })}
      </div>
      <br />
    </div>
  );
}

/**
 * @param {PartialSearchResult<'pages'>}
 */
function SearchResultsPages({term, pages}) {
  if (!pages?.nodes.length) {
    return null;
  }

  return (
    <div className="search-result">
      <h2>Pages</h2>
      <div>
        {pages?.nodes?.map((page) => {
          const pageUrl = urlWithTrackingParams({
            baseUrl: `/pages/${page.handle}`,
            trackingParams: page.trackingParameters,
            term,
          });

          return (
            <div className="search-results-item" key={page.id}>
              <Link prefetch="intent" to={pageUrl}>
                {page.title}
              </Link>
            </div>
          );
        })}
      </div>
      <br />
    </div>
  );
}

/**
 * @param {PartialSearchResult<'products'>}
 */
function SearchResultsProducts({term, products, customer}) {
  if (!products?.nodes.length) {
    return null;
  }

  return (
    <div className="search-result">
      {/* <h2 className="font-medium text-xl p-2">Products</h2> */}
      <Pagination connection={products}>
        {({nodes, isLoading, NextLink, PreviousLink}) => {
          const ItemsMarkup = nodes.map((product) => {
            const productUrl = urlWithTrackingParams({
              baseUrl: `/products/${product.handle}`,
              trackingParams: product.trackingParameters,
              term,
            });
            const variant = product.variants.nodes[0];

            return (
              <ImageCard
                key={product.id}
                to={productUrl}
                title={product.title}
                imageData={variant.image}
              >
                <small className="text-lg">
                  <Suspense fallback={<Loading />}>
                    <Await resolve={customer}>
                      {({status: {isLoggedIn, isApproved}}) =>
                        isLoggedIn &&
                        isApproved && (
                          <ProductPrice
                            price={variant?.price}
                            compareAtPrice={variant?.compareAtPrice}
                          />
                        )
                      }
                    </Await>
                  </Suspense>
                </small>
              </ImageCard>
            );
          });

          return (
            <div>
              <div>
                <PreviousLink>
                  {isLoading ? <Loading /> : <span>↑ Load previous</span>}
                </PreviousLink>
              </div>
              <div className="products-grid">{ItemsMarkup}</div>
              <div>
                <NextLink>
                  {isLoading ? <Loading /> : <span>Load more ↓</span>}
                </NextLink>
              </div>
            </div>
          );
        }}
      </Pagination>
      <br />
    </div>
  );
}

function SearchResultsEmpty() {
  return <p>No results, try a different search.</p>;
}

/** @typedef {RegularSearchReturn['result']['items']} SearchItems */
/**
 * @typedef {Pick<
 *   SearchItems,
 *   ItemType
 * > &
 *   Pick<RegularSearchReturn, 'term'>} PartialSearchResult
 * @template {keyof SearchItems} ItemType
 */
/**
 * @typedef {RegularSearchReturn & {
 *   children: (args: SearchItems & {term: string}) => React.ReactNode;
 * }} SearchResultsProps
 */

/** @typedef {import('~/lib/search').RegularSearchReturn} RegularSearchReturn */
