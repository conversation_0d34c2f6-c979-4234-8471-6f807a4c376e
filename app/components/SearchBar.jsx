import {forwardRef} from 'react';
import {Input} from '~/components/ui/input';
import iconSearch from '~/assets/search.svg';
import {cn} from '~/lib/utils';

export const SearchBar = forwardRef(({containerClassName, ...props}, ref) => {
  return (
    <div
      className={cn('relative flex bg-white rounded-lg', containerClassName)}
    >
      <Input ref={ref} {...props} />
      <button type="submit" className="absolute h-full left-0 p-1 rounded-r">
        <img src={iconSearch} alt="search" className="w-[25px]" />
      </button>
    </div>
  );
});
