import {useState} from 'react';
import {Button} from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {EditProfileForm} from '~/components/EditProfileForm';

export function ProfileDialog({customerDetails}) {
  const [open, setOpen] = useState(true);
  if (!customerDetails) return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Finish Account Setup</DialogTitle>
          <DialogDescription>
            To view prices and start shopping, please request account approval
            by filling out the details below.
          </DialogDescription>
        </DialogHeader>
        <EditProfileForm customerDetails={customerDetails}></EditProfileForm>
      </DialogContent>
      <DialogFooter></DialogFooter>
    </Dialog>
  );
}
