import {useState, useRef, useEffect} from 'react';
import {motion, AnimatePresence} from 'motion/react';
import {MessageCircle, Send, X, Bot, User, Loader2} from 'lucide-react';
import {cn} from '~/lib/utils';

export function AIChatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: "Hi! I'm your lab equipment assistant. How can I help you find the right equipment today?",
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      const botResponse = {
        id: Date.now() + 1,
        type: 'bot',
        content: generateBotResponse(inputValue),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, botResponse]);
      setIsLoading(false);
    }, 1000 + Math.random() * 1000);
  };

  const generateBotResponse = (userInput) => {
    const input = userInput.toLowerCase();
    
    if (input.includes('price') || input.includes('cost')) {
      return "I'd be happy to help with pricing information! Please create an account or log in to view our competitive institutional pricing. Our pricing varies based on volume and institutional partnerships.";
    }
    
    if (input.includes('stirrer') || input.includes('magnetic')) {
      return "We have a great selection of magnetic stirrers! Our current promotion includes special pricing on magnetic stirrers. Would you like me to show you our most popular models or help you find one for specific requirements?";
    }
    
    if (input.includes('fume hood') || input.includes('ventilation')) {
      return "Fume hoods are essential for laboratory safety. We offer various sizes and configurations. What type of work will you be doing, and do you have any specific ventilation requirements?";
    }
    
    if (input.includes('certification') || input.includes('compliance')) {
      return "All our equipment comes with research-grade certifications and complete documentation to ensure compliance with Australian research standards and regulatory requirements.";
    }
    
    if (input.includes('support') || input.includes('help')) {
      return "Our specialized support team provides expert technical assistance for complex research applications. We offer installation support, training, and ongoing maintenance services.";
    }
    
    return "That's a great question! I can help you find the right laboratory equipment for your research needs. Could you tell me more about what specific equipment or application you're looking for?";
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) {
    return (
      <motion.button
        initial={{scale: 0}}
        animate={{scale: 1}}
        whileHover={{scale: 1.05}}
        whileTap={{scale: 0.95}}
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 bg-gba-blue hover:bg-blue-600 text-white p-4 rounded-full shadow-lg transition-colors z-50"
      >
        <MessageCircle className="h-6 w-6" />
      </motion.button>
    );
  }

  return (
    <motion.div
      initial={{opacity: 0, y: 20, scale: 0.95}}
      animate={{opacity: 1, y: 0, scale: 1}}
      exit={{opacity: 0, y: 20, scale: 0.95}}
      className="fixed bottom-6 right-6 w-80 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gba-blue text-white rounded-t-lg">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          <span className="font-medium">Lab Assistant</span>
        </div>
        <button
          onClick={() => setIsOpen(false)}
          className="hover:bg-blue-600 p-1 rounded transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex gap-2',
              message.type === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            {message.type === 'bot' && (
              <div className="flex-shrink-0 w-8 h-8 bg-gba-blue rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
            )}
            <div
              className={cn(
                'max-w-[70%] p-3 rounded-lg text-sm',
                message.type === 'user'
                  ? 'bg-gba-blue text-white rounded-br-none'
                  : 'bg-gray-100 text-gray-800 rounded-bl-none'
              )}
            >
              {message.content}
            </div>
            {message.type === 'user' && (
              <div className="flex-shrink-0 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-600" />
              </div>
            )}
          </div>
        ))}
        
        {isLoading && (
          <div className="flex gap-2 justify-start">
            <div className="flex-shrink-0 w-8 h-8 bg-gba-blue rounded-full flex items-center justify-center">
              <Bot className="h-4 w-4 text-white" />
            </div>
            <div className="bg-gray-100 p-3 rounded-lg rounded-bl-none">
              <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about lab equipment..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gba-blue focus:border-transparent text-sm"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-gba-blue hover:bg-blue-600 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </motion.div>
  );
}
