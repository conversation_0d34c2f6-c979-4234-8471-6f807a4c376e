// Shared chatbot utilities and response logic

export const generateBotResponse = (userInput) => {
  const input = userInput.toLowerCase();

  if (input.includes('quote')) {
    return "To generate a quote, simply add items to your cart and click the 'Create Quote' button in your cart. This will automatically generate a detailed quote for your selected equipment!";
  }

  if (input.includes('price') || input.includes('cost')) {
    return "I'd be happy to help with pricing information! Please create an account or log in to view our competitive institutional pricing. Our pricing varies based on volume and institutional partnerships.";
  }

  if (input.includes('stirrer') || input.includes('magnetic')) {
    return "We have a great selection of magnetic stirrers! Our current promotion includes special pricing on magnetic stirrers. Would you like me to show you our most popular models or help you find one for specific requirements?";
  }

  if (input.includes('fume hood') || input.includes('ventilation')) {
    return "Fume hoods are essential for laboratory safety. We offer various sizes and configurations. What type of work will you be doing, and do you have any specific ventilation requirements?";
  }

  if (input.includes('certification') || input.includes('compliance')) {
    return "All our equipment comes with research-grade certifications and complete documentation to ensure compliance with Australian research standards and regulatory requirements.";
  }

  if (input.includes('support') || input.includes('help')) {
    return "Our specialized support team provides expert technical assistance for complex research applications. We offer installation support, training, and ongoing maintenance services.";
  }

  if (input.includes('hello') || input.includes('hi')) {
    return "Hello! I'm here to help you find the perfect lab equipment. What type of equipment are you interested in?";
  }

  return "That's a great question! I can help you find the right laboratory equipment for your research needs. Could you tell me more about what specific equipment or application you're looking for?";
};

export const simulateAIResponse = (userInput, callback, setIsLoading) => {
  setIsLoading(true);
  
  // Simulate AI response delay
  setTimeout(() => {
    const botResponse = {
      id: Date.now() + 1,
      type: 'bot',
      content: generateBotResponse(userInput),
      timestamp: new Date(),
    };
    callback(botResponse);
    setIsLoading(false);
  }, 800 + Math.random() * 800);
};

export const handleKeyPress = (e, handleSendMessage) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    handleSendMessage();
  }
};

export const createUserMessage = (content) => ({
  id: Date.now(),
  type: 'user',
  content,
  timestamp: new Date(),
});

export const createInitialBotMessage = (isInline = false) => ({
  id: 1,
  type: 'bot',
  content: isInline 
    ? "Hi! I can help you find the right lab equipment. What are you looking for?"
    : "Hi! I'm your lab equipment assistant. How can I help you find the right equipment today?",
  timestamp: new Date(),
});

export const quickActions = [
  "Show me magnetic stirrers",
  "What's on promotion?",
  "Generate a quote for me",
  "I need technical support"
];
