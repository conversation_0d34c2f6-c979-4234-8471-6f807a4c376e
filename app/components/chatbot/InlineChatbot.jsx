import {useState, useEffect} from 'react';
import {motion, AnimatePresence} from 'motion/react';
import {Send, Bot, ChevronUp, ChevronDown} from 'lucide-react';
import {cn} from '~/lib/utils';
import {useChatbot} from './useChatbot';
import {ChatMessage, LoadingMessage} from './ChatMessage';
import {quickActions} from './chatbotUtils';

export function InlineChatbot() {
  const [isExpanded, setIsExpanded] = useState(false);
  const {
    messages,
    inputValue,
    setInputValue,
    isLoading,
    messagesContainerRef,
    inputRef,
    handleSendMessage,
    handleKeyPress,
    focusInput,
  } = useChatbot(true);

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded) {
      focusInput();
    }
  }, [isExpanded, focusInput]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gba-blue rounded-full flex items-center justify-center">
            <Bot className="h-4 w-4 text-white" />
          </div>
          <div className="text-left">
            <h3 className="font-medium text-gray-900">Lab Assistant</h3>
            <p className="text-xs text-gray-500">Ask me about equipment</p>
          </div>
        </div>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4 text-gray-400" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-400" />
        )}
      </button>

      {/* Chat Interface */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{height: 0, opacity: 0}}
            animate={{height: 'auto', opacity: 1}}
            exit={{height: 0, opacity: 0}}
            transition={{duration: 0.2}}
            className="border-t border-gray-200 overflow-hidden"
          >
            {/* Messages */}
            <div
              ref={messagesContainerRef}
              className="h-48 overflow-y-auto p-3 space-y-2 bg-gray-50"
            >
              {messages.map((message) => (
                <ChatMessage key={message.id} message={message} size="small" />
              ))}

              {isLoading && <LoadingMessage size="small" />}
            </div>

            {/* Quick Actions */}
            {/* messages.length === 1 &&  */}
            {
              <div className="p-3 border-t border-gray-200 bg-white">
                <p className="text-xs text-gray-500 mb-2">Quick questions:</p>
                <div className="space-y-1">
                  {quickActions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setInputValue(action);
                        setTimeout(handleSendMessage, 100);
                      }}
                      className="w-full text-left text-xs p-2 hover:bg-gray-50 rounded border border-gray-200 transition-colors"
                    >
                      {action}
                    </button>
                  ))}
                </div>
              </div>
            }

            {/* Input */}
            <div className="p-3 border-t border-gray-200 bg-white">
              <div className="flex gap-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask about equipment..."
                  className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gba-blue focus:border-transparent"
                  disabled={isLoading}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  className="bg-gba-blue hover:bg-blue-600 disabled:bg-gray-300 text-white p-1 rounded transition-colors"
                >
                  <Send className="h-3 w-3" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
