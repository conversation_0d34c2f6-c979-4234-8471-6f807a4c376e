import {useState, useRef, useEffect} from 'react';
import {motion, AnimatePresence} from 'motion/react';
import {
  Send,
  Bot,
  User,
  Loader2,
  MessageCircle,
  ChevronUp,
  ChevronDown,
} from 'lucide-react';
import {cn} from '~/lib/utils';

export function InlineChatbot() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content:
        'Hi! I can help you find the right lab equipment. What are you looking for?',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const inputRef = useRef(null);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isExpanded && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isExpanded]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response (replace with actual API call)
    setTimeout(
      () => {
        const botResponse = {
          id: Date.now() + 1,
          type: 'bot',
          content: generateBotResponse(inputValue),
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, botResponse]);
        setIsLoading(false);
      },
      800 + Math.random() * 800,
    );
  };

  const generateBotResponse = (userInput) => {
    const input = userInput.toLowerCase();

    if (input.includes('price') || input.includes('cost')) {
      return 'Create an account to view our competitive institutional pricing!';
    }

    if (input.includes('stirrer') || input.includes('magnetic')) {
      return 'Great choice! We have magnetic stirrers on special promotion right now. Check out our current deals!';
    }

    if (input.includes('fume hood') || input.includes('ventilation')) {
      return 'We offer various fume hood configurations. What size lab space are you working with?';
    }

    if (input.includes('certification') || input.includes('compliance')) {
      return 'All our equipment includes research-grade certifications and compliance documentation.';
    }

    if (input.includes('support') || input.includes('help')) {
      return 'Our expert team provides technical support, installation, and training services.';
    }

    if (input.includes('hello') || input.includes('hi')) {
      return "Hello! I'm here to help you find the perfect lab equipment. What type of equipment are you interested in?";
    }

    return "I can help you find the right equipment! Could you tell me more about your specific needs or the type of research you're conducting?";
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const quickActions = [
    'Show me magnetic stirrers',
    "What's on promotion?",
    'I need pricing info',
    'Technical support',
  ];

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gba-blue rounded-full flex items-center justify-center">
            <Bot className="h-4 w-4 text-white" />
          </div>
          <div className="text-left">
            <h3 className="font-medium text-gray-900">Lab Assistant</h3>
            <p className="text-xs text-gray-500">Ask me about equipment</p>
          </div>
        </div>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4 text-gray-400" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-400" />
        )}
      </button>

      {/* Chat Interface */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{height: 0, opacity: 0}}
            animate={{height: 'auto', opacity: 1}}
            exit={{height: 0, opacity: 0}}
            transition={{duration: 0.2}}
            className="border-t border-gray-200 overflow-hidden"
          >
            {/* Messages */}
            <div
              ref={messagesContainerRef}
              className="h-40 overflow-y-auto p-3 space-y-2 bg-gray-50"
            >
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    'flex gap-2 text-xs',
                    message.type === 'user' ? 'justify-end' : 'justify-start',
                  )}
                >
                  {message.type === 'bot' && (
                    <div className="flex-shrink-0 w-6 h-6 bg-gba-blue rounded-full flex items-center justify-center">
                      <Bot className="h-3 w-3 text-white" />
                    </div>
                  )}
                  <div
                    className={cn(
                      'max-w-[80%] p-2 rounded-lg',
                      message.type === 'user'
                        ? 'bg-gba-blue text-white rounded-br-none'
                        : 'bg-white text-gray-800 rounded-bl-none border border-gray-200',
                    )}
                  >
                    {message.content}
                  </div>
                  {message.type === 'user' && (
                    <div className="flex-shrink-0 w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                      <User className="h-3 w-3 text-gray-600" />
                    </div>
                  )}
                </div>
              ))}

              {isLoading && (
                <div className="flex gap-2 justify-start text-xs">
                  <div className="flex-shrink-0 w-6 h-6 bg-gba-blue rounded-full flex items-center justify-center">
                    <Bot className="h-3 w-3 text-white" />
                  </div>
                  <div className="bg-white p-2 rounded-lg rounded-bl-none border border-gray-200">
                    <Loader2 className="h-3 w-3 animate-spin text-gray-500" />
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Quick Actions */}
            {/* messages.length === 1 &&  */}
            {
              <div className="p-3 border-t border-gray-200 bg-white">
                <p className="text-xs text-gray-500 mb-2">Quick questions:</p>
                <div className="space-y-1">
                  {quickActions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setInputValue(action);
                        setTimeout(handleSendMessage, 100);
                      }}
                      className="w-full text-left text-xs p-2 hover:bg-gray-50 rounded border border-gray-200 transition-colors"
                    >
                      {action}
                    </button>
                  ))}
                </div>
              </div>
            }

            {/* Input */}
            <div className="p-3 border-t border-gray-200 bg-white">
              <div className="flex gap-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask about equipment..."
                  className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gba-blue focus:border-transparent"
                  disabled={isLoading}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  className="bg-gba-blue hover:bg-blue-600 disabled:bg-gray-300 text-white p-1 rounded transition-colors"
                >
                  <Send className="h-3 w-3" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
