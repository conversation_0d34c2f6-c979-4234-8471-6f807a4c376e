import {useState, useEffect} from 'react';
import {motion, AnimatePresence} from 'motion/react';
import {Send, Bot, MessageCircle, X} from 'lucide-react';
import {cn} from '~/lib/utils';
import {useChatbot} from './useChatbot';
import {ChatMessage, LoadingMessage} from './ChatMessage';
import {quickActions} from './chatbotUtils';

export function InlineChatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const {
    messages,
    inputValue,
    setInputValue,
    isLoading,
    messagesContainerRef,
    inputRef,
    handleSendMessage,
    handleKeyPress,
    focusInput,
  } = useChatbot(true);

  // Focus input when opened
  useEffect(() => {
    if (isOpen) {
      focusInput();
    }
  }, [isOpen, focusInput]);

  if (!isOpen) {
    return (
      <motion.button
        initial={{scale: 0}}
        animate={{scale: 1}}
        whileHover={{scale: 1.05}}
        whileTap={{scale: 0.95}}
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 bg-gba-blue hover:bg-blue-600 text-white p-4 rounded-full shadow-lg transition-colors z-50"
      >
        <MessageCircle className="h-6 w-6" />
      </motion.button>
    );
  }

  return (
    <motion.div
      initial={{opacity: 0, y: 20, scale: 0.95}}
      animate={{opacity: 1, y: 0, scale: 1}}
      exit={{opacity: 0, y: 20, scale: 0.95}}
      className="fixed bottom-6 right-6 w-80 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gba-blue text-white rounded-t-lg">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          <span className="font-medium">Lab Assistant</span>
        </div>
        <button
          onClick={() => setIsOpen(false)}
          className="hover:bg-blue-600 p-1 rounded transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-3"
      >
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}

        {isLoading && <LoadingMessage />}
      </div>

      {/* Quick Actions */}
      {messages.length === 1 && (
        <div className="p-3 border-t border-gray-200 bg-white">
          <p className="text-xs text-gray-500 mb-2">Quick questions:</p>
          <div className="space-y-1">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={() => {
                  setInputValue(action);
                  setTimeout(handleSendMessage, 100);
                }}
                className="w-full text-left text-xs p-2 hover:bg-gray-50 rounded border border-gray-200 transition-colors"
              >
                {action}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about equipment..."
            className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gba-blue focus:border-transparent"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-gba-blue hover:bg-blue-600 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </motion.div>
  );
}
