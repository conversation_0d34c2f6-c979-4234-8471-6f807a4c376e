import {Bo<PERSON>, User, Loader2} from 'lucide-react';
import {cn} from '~/lib/utils';

export function ChatMessage({message, size = 'default'}) {
  const isUser = message.type === 'user';
  const isSmall = size === 'small';
  
  const iconSize = isSmall ? 'h-3 w-3' : 'h-4 w-4';
  const avatarSize = isSmall ? 'w-6 h-6' : 'w-8 h-8';
  const textSize = isSmall ? 'text-xs' : 'text-sm';
  const padding = isSmall ? 'p-2' : 'p-3';
  const maxWidth = isSmall ? 'max-w-[80%]' : 'max-w-[70%]';

  return (
    <div
      className={cn(
        'flex gap-2',
        isUser ? 'justify-end' : 'justify-start'
      )}
    >
      {!isUser && (
        <div className={cn(
          'flex-shrink-0 bg-gba-blue rounded-full flex items-center justify-center',
          avatarSize
        )}>
          <Bot className={cn('text-white', iconSize)} />
        </div>
      )}
      
      <div
        className={cn(
          maxWidth,
          padding,
          'rounded-lg',
          textSize,
          isUser
            ? 'bg-gba-blue text-white rounded-br-none'
            : isSmall 
              ? 'bg-white text-gray-800 rounded-bl-none border border-gray-200'
              : 'bg-gray-100 text-gray-800 rounded-bl-none'
        )}
      >
        {message.content}
      </div>
      
      {isUser && (
        <div className={cn(
          'flex-shrink-0 bg-gray-300 rounded-full flex items-center justify-center',
          avatarSize
        )}>
          <User className={cn('text-gray-600', iconSize)} />
        </div>
      )}
    </div>
  );
}

export function LoadingMessage({size = 'default'}) {
  const isSmall = size === 'small';
  const iconSize = isSmall ? 'h-3 w-3' : 'h-4 w-4';
  const avatarSize = isSmall ? 'w-6 h-6' : 'w-8 h-8';
  const padding = isSmall ? 'p-2' : 'p-3';

  return (
    <div className="flex gap-2 justify-start">
      <div className={cn(
        'flex-shrink-0 bg-gba-blue rounded-full flex items-center justify-center',
        avatarSize
      )}>
        <Bot className={cn('text-white', iconSize)} />
      </div>
      <div className={cn(
        padding,
        'rounded-lg rounded-bl-none',
        isSmall 
          ? 'bg-white border border-gray-200'
          : 'bg-gray-100'
      )}>
        <Loader2 className={cn('animate-spin text-gray-500', iconSize)} />
      </div>
    </div>
  );
}
