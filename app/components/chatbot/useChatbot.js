import {useState, useRef, useEffect} from 'react';
import {createUserMessage, createInitialBotMessage, simulateAIResponse} from './chatbotUtils';

export function useChatbot(isInline = false) {
  const [messages, setMessages] = useState([createInitialBotMessage(isInline)]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const inputRef = useRef(null);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = createUserMessage(inputValue);
    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');

    // Simulate AI response
    simulateAIResponse(
      currentInput,
      (botResponse) => {
        setMessages(prev => [...prev, botResponse]);
      },
      setIsLoading
    );
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const focusInput = () => {
    if (inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  };

  return {
    messages,
    inputValue,
    setInputValue,
    isLoading,
    messagesEndRef,
    messagesContainerRef,
    inputRef,
    handleSendMessage,
    handleKeyPress,
    focusInput,
    scrollToBottom
  };
}
