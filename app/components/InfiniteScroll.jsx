import {useEffect} from 'react';

import {useNavigate} from '@remix-run/react';

export function InfiniteScroll({
  children,
  className,
  inView,
  hasNextPage,
  nextPageUrl,
  state,
}) {
  const navigate = useNavigate();

  useEffect(() => {
    if (inView && hasNextPage) {
      navigate(nextPageUrl, {
        replace: true,
        preventScrollReset: true,
        state,
      });
    }
  }, [inView, navigate, state, nextPageUrl, hasNextPage]);

  return <div className={className}>{children}</div>;
}
