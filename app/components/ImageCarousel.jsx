import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '~/components/ui/carousel';
import {Image} from '@shopify/hydrogen';

export function ImageCarousel({images}) {
  return (
    <Carousel className="max-w-[300px] mt-2" opts={{loop: true}}>
      <CarouselContent>
        {images?.map((image) => (
          <CarouselItem
            key={image.id}
            className="basis-1/2 flex justify-center items-center"
          >
            <Image
              data={image}
              sizes="(min-width: 45em) 50vw, 100vw"
              className="h-[55px] w-auto"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
}
