import {useState} from 'react';
import {AnimatePresence, motion} from 'motion/react';
import {Link, NavLink} from '@remix-run/react';
import PageLink from '~/components/common/PageLink';
import {Image} from '@shopify/hydrogen';
import {CaretDown, CaretRight} from '~/components/common/Caret';
import {cn, getMenuUrl} from '~/lib/utils';

export function NavMenu({menu}) {
  const [selectedNavItem, setSelectedNavItem] = useState(null);

  return (
    <nav className="relative header-menu-desktop">
      <ul className="flex gap-4">
        {menu?.items.map((navItem) => {
          const menuItems = navItem.items || [];
          const hasMenuItems = menuItems.length > 0;
          const isOpen = hasMenuItems && selectedNavItem?.id === navItem.id;
          //const isOpen = true;

          return (
            <li key={navItem.id} className="relative size-full">
              <PageLink
                to={getMenuUrl(navItem)}
                onMouseEnter={() => setSelectedNavItem(navItem)}
                onMouseLeave={() => setSelectedNavItem(null)}
                onClick={() => setSelectedNavItem(null)}
                className={`size-full flex justify-center items-center 
                    font-medium text-sm lg:text-xl whitespace-nowrap p-4
                    border-current
                    ${isOpen && 'border-b-2'}`}
                prefetch="intent"
              >
                {navItem.title}
                {hasMenuItems && (
                  <CaretDown
                    active={isOpen}
                    className="relative top-[1px] ml-1 h-4 w-4"
                  />
                )}
              </PageLink>
              <AnimatePresence initial={false}>
                {isOpen && (
                  <Drawer
                    item={navItem}
                    menuItems={menuItems}
                    onClick={() => setSelectedNavItem(null)}
                    onMouseEnter={() => setSelectedNavItem(navItem)}
                    onMouseLeave={() => setSelectedNavItem(null)}
                    className="w-[400px] lg:w-[600px] pt-2"
                  />
                )}
              </AnimatePresence>
            </li>
          );
        })}
      </ul>
    </nav>
  );
}

function Drawer({item, menuItems, className, onClick, ...props}) {
  const [selectedItem, setSelectedItem] = useState(null);
  return (
    <motion.div
      initial={{opacity: 0, y: -20}}
      animate={{opacity: 1, y: 0}}
      exit={{opacity: 0, y: -20}}
      className={cn('absolute top-full', className)}
      {...props}
    >
      <div
        onMouseLeave={() => setSelectedItem(null)}
        className="bg-[#e3e7eb] grid grid-cols-2 py-4 round shadow-lg"
      >
        <ul className="flex flex-col">
          {menuItems.map((menuItem) => {
            const isSelected = selectedItem?.id === menuItem.id;
            return (
              <li key={menuItem.id}>
                <PageLink
                  to={getMenuUrl(menuItem)}
                  onClick={onClick}
                  onMouseEnter={() => setSelectedItem(menuItem)}
                  className={`relative flex flex-row items-center font-medium px-4 py-1 
                    ${isSelected ? 'opacity-100' : selectedItem && 'opacity-50'} transition`}
                  prefetch="intent"
                >
                  {menuItem.title}
                  {menuItem?.items.length > 0 && (
                    <CaretRight
                      active={isSelected}
                      className="absolute right-2 top-[30%] ml-1 h-3 w-3"
                    />
                  )}
                </PageLink>
              </li>
            );
          })}
        </ul>
        <div className="flex flex-col px-4">
          <p className="font-bold text-lg p-1">
            {selectedItem ? selectedItem.title : item.title}
          </p>
          <ul>
            {selectedItem?.items.map((subItem) => {
              return (
                <li key={subItem.id}>
                  <PageLink
                    to={getMenuUrl(subItem)}
                    onClick={onClick}
                    className="p-1"
                    prefetch="intent"
                  >
                    {subItem.title}
                  </PageLink>
                </li>
              );
            })}
          </ul>
          <MenuImage item={selectedItem ? selectedItem : item} />
        </div>
      </div>
    </motion.div>
  );
}

function MenuImage({item}) {
  const imageData = item?.resource?.image;
  if (imageData) {
    return (
      <Link prefetch="intent" to={getMenuUrl(item)} className="p-1">
        <Image
          data={imageData}
          sizes="(min-width: 45em) 50vw, 100vw"
          className="max-w-[200px] round shadow"
        />
      </Link>
    );
  }

  return null;
}
